{"server": {"port": 8080, "host": "0.0.0.0", "maxConnections": 200, "heartbeatInterval": 30000, "messageTimeout": 30000, "enableCompression": false}, "security": {"enableEncryption": true, "keyRotationInterval": 86400000, "maxMessageSize": 104857600, "enableAuthentication": false, "sessionTimeout": 86400000}, "logging": {"level": "INFO", "enableConsole": true, "enableFile": true, "logDirectory": "./logs", "maxFileSize": 10485760, "maxFiles": 5}, "plugins": {"directory": "./plugins", "maxPluginSize": 52428800, "enableAutoUpdate": false, "executionTimeout": 300000, "maxConcurrentExecutions": 10}, "database": {"type": "memory", "filePath": "./data/database.json", "autoSave": true, "saveInterval": 60000}, "performance": {"enableMetrics": true, "metricsInterval": 60000, "enableGarbageCollection": true, "gcInterval": 300000}, "features": {"enableWebUI": false, "webUIPort": 8081, "enableAPI": true, "apiPort": 8082, "enableFileTransfer": true, "enableRemoteShell": false}, "limits": {"maxTasksPerClient": 5, "maxFileSize": 1073741824, "maxChunkSize": 65536, "maxConcurrentChunks": 10, "connectionRateLimit": 100}}