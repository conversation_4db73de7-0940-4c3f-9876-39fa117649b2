// 解密测试工具
const crypto = require('crypto');

class CryptoUtils {
    constructor() {
        // 与服务器相同的硬编码密钥
        this.defaultKey = Buffer.from([
            0x4A, 0x7B, 0x9C, 0x2D, 0x8E, 0x5F, 0x31, 0xA6,
            0xB7, 0x48, 0x19, 0xCA, 0x3B, 0xDC, 0x6E, 0x4F,
            0x82, 0x53, 0x94, 0x25, 0xE6, 0x17, 0x78, 0x9A,
            0x1B, 0xFC, 0x2D, 0x8E, 0x5F, 0x60, 0x91, 0xC2
        ]);
    }

    // XOR解密
    xorCrypt(data, key = null) {
        try {
            const cryptKey = key || this.defaultKey;
            
            let inputBuffer;
            if (typeof data === 'string') {
                inputBuffer = Buffer.from(data, 'utf8');
            } else if (Buffer.isBuffer(data)) {
                inputBuffer = data;
            } else {
                throw new Error('数据类型不支持');
            }

            const result = Buffer.alloc(inputBuffer.length);
            
            for (let i = 0; i < inputBuffer.length; i++) {
                result[i] = inputBuffer[i] ^ cryptKey[i % cryptKey.length];
            }
            
            return result;
        } catch (error) {
            console.error('XOR解密失败:', error);
            throw error;
        }
    }

    // 解密Base64编码的字符串
    decryptString(encryptedBase64, key = null) {
        try {
            const encryptedBuffer = Buffer.from(encryptedBase64, 'base64');
            const decrypted = this.xorCrypt(encryptedBuffer, key);
            return decrypted.toString('utf8');
        } catch (error) {
            console.error('解密字符串失败:', error);
            throw error;
        }
    }
}

// 测试解密
const cryptoUtils = new CryptoUtils();

// 从测试输出中获取的加密错误消息
const encryptedMessage = "MVn5X/wwQ4SNMzupVLgLbbhx3WuwVjTTX6Ngywwz0IUPJMh03hoTipUlfLlIvQkqoGm2w3q9nwW+GpsGueE+JfvAebMFZRHB0jxGqVe1CyH2DP1LgHhatjmIROM6E+WjJwu+F6xtAZSCZSnyFuxcG7NjrhfeLUiiNcsauwVC7L8=";

try {
    console.log('🔓 解密服务器错误消息:');
    const decrypted = cryptoUtils.decryptString(encryptedMessage);
    console.log('解密结果:', decrypted);
    
    // 尝试解析为JSON
    try {
        const parsed = JSON.parse(decrypted);
        console.log('解析后的JSON:', JSON.stringify(parsed, null, 2));
    } catch (e) {
        console.log('不是有效的JSON格式');
    }
} catch (error) {
    console.error('解密失败:', error);
}
