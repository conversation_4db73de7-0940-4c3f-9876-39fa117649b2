#pragma once

#include "common.h"
#include <string>
#include <array>

namespace Obfuscator {

    // Compile-time string encryption
    template<size_t N>
    struct ObfuscatedString {
        constexpr ObfuscatedString(const char (&str)[N]) : encrypted_(), key_(GenerateKey()) {
            for (size_t i = 0; i < N; ++i) {
                encrypted_[i] = str[i] ^ key_;
            }
        }
        
        std::string Decrypt() const {
            std::string result;
            result.reserve(N - 1); // -1 for null terminator
            
            for (size_t i = 0; i < N - 1; ++i) {
                result += static_cast<char>(encrypted_[i] ^ key_);
            }
            
            return result;
        }
        
        const char* DecryptCStr() const {
            static char buffer[N];
            for (size_t i = 0; i < N; ++i) {
                buffer[i] = encrypted_[i] ^ key_;
            }
            return buffer;
        }
        
    private:
        std::array<char, N> encrypted_;
        uint8_t key_;
        
        constexpr uint8_t GenerateKey() const {
            // Simple compile-time key generation based on string content
            uint8_t seed = 0x5A;
            for (size_t i = 0; i < N && i < 8; ++i) {
                seed ^= static_cast<uint8_t>(i * 7 + 13);
            }
            return seed;
        }
    };

    // Macro for creating obfuscated strings
    #define OBFUSCATE(str) []() { \
        constexpr auto obfs = Obfuscator::ObfuscatedString(str); \
        return obfs.Decrypt(); \
    }()

    #define OBFUSCATE_CSTR(str) []() { \
        constexpr auto obfs = Obfuscator::ObfuscatedString(str); \
        return obfs.DecryptCStr(); \
    }()

    // Runtime string obfuscation
    class RuntimeStringObfuscator {
    public:
        static std::string Encrypt(const std::string& input, uint8_t key = 0);
        static std::string Decrypt(const std::string& encrypted, uint8_t key = 0);
        
        // Base64 variants
        static std::string EncryptToBase64(const std::string& input, uint8_t key = 0);
        static std::string DecryptFromBase64(const std::string& base64_encrypted, uint8_t key = 0);
        
        // Advanced obfuscation
        static std::string AdvancedEncrypt(const std::string& input);
        static std::string AdvancedDecrypt(const std::string& encrypted);
        
        // Base64 helpers
        static std::string Base64Encode(const std::string& input);
        static std::string Base64Decode(const std::string& input);
        
    private:
        static uint8_t GenerateKey(const std::string& input);
    };

    // Function name obfuscation for dynamic loading
    class FunctionNameObfuscator {
    public:
        static std::string ObfuscateName(const std::string& original_name);
        static std::string DeobfuscateName(const std::string& obfuscated_name);
        
        // Hash-based obfuscation
        static std::string HashObfuscate(const std::string& name);
        static bool VerifyHashObfuscated(const std::string& name, const std::string& hash);
        
    private:
        static uint32_t SimpleHash(const std::string& input);
        static std::string RotateString(const std::string& input, int rotation);
    };

    // Stack string protection
    template<size_t MaxLen = 256>
    class StackString {
    public:
        StackString() : length_(0) {
            SecureZeroMemory(data_, sizeof(data_));
        }
        
        StackString(const char* str) : length_(0) {
            SecureZeroMemory(data_, sizeof(data_));
            Assign(str);
        }
        
        StackString(const std::string& str) : length_(0) {
            SecureZeroMemory(data_, sizeof(data_));
            Assign(str.c_str());
        }
        
        ~StackString() {
            Clear();
        }
        
        void Assign(const char* str) {
            Clear();
            if (str) {
                length_ = std::min(strlen(str), MaxLen - 1);
                memcpy(data_, str, length_);
                data_[length_] = '\0';
                
                // Simple obfuscation
                for (size_t i = 0; i < length_; ++i) {
                    data_[i] ^= static_cast<char>(0xAA ^ (i & 0xFF));
                }
            }
        }
        
        std::string GetString() const {
            char temp[MaxLen];
            SecureZeroMemory(temp, sizeof(temp));
            
            // Deobfuscate
            for (size_t i = 0; i < length_; ++i) {
                temp[i] = data_[i] ^ static_cast<char>(0xAA ^ (i & 0xFF));
            }
            temp[length_] = '\0';
            
            std::string result(temp);
            SecureZeroMemory(temp, sizeof(temp));
            return result;
        }
        
        const char* GetCString() const {
            static char buffer[MaxLen];
            SecureZeroMemory(buffer, sizeof(buffer));
            
            // Deobfuscate
            for (size_t i = 0; i < length_; ++i) {
                buffer[i] = data_[i] ^ static_cast<char>(0xAA ^ (i & 0xFF));
            }
            buffer[length_] = '\0';
            
            return buffer;
        }
        
        void Clear() {
            SecureZeroMemory(data_, sizeof(data_));
            length_ = 0;
        }
        
        bool IsEmpty() const {
            return length_ == 0;
        }
        
        size_t Length() const {
            return length_;
        }
        
    private:
        char data_[MaxLen];
        size_t length_;
        
        void SecureZeroMemory(void* ptr, size_t size) {
            volatile char* p = static_cast<volatile char*>(ptr);
            while (size--) {
                *p++ = 0;
            }
        }
    };

    // Anti-debugging strings
    class AntiDebugStrings {
    public:
        static bool IsDebuggerPresent();
        static bool CheckDebuggerProcesses();
        static std::vector<std::string> GetDebuggerProcessNames();
        
    private:
        static std::vector<std::string> GetObfuscatedDebuggerNames();
    };

    // String encryption at different security levels
    namespace Security {
        enum class Level {
            NONE = 0,
            BASIC = 1,
            MEDIUM = 2,
            HIGH = 3,
            MAXIMUM = 4
        };
        
        std::string EncryptString(const std::string& input, Level level = Level::MEDIUM);
        std::string DecryptString(const std::string& encrypted, Level level = Level::MEDIUM);
        
        // Level-specific encryption functions
        std::string BasicEncrypt(const std::string& input);
        std::string MediumEncrypt(const std::string& input);
        std::string HighEncrypt(const std::string& input);
        std::string MaximumEncrypt(const std::string& input);
        
        std::string BasicDecrypt(const std::string& encrypted);
        std::string MediumDecrypt(const std::string& encrypted);
        std::string HighDecrypt(const std::string& encrypted);
        std::string MaximumDecrypt(const std::string& encrypted);
    }

} // namespace Obfuscator