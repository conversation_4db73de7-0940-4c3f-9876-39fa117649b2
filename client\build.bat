@echo off
setlocal

echo Building Distributed Plugin Client...

:: Set build directory
set BUILD_DIR=build
set CMAKE_BUILD_TYPE=Release

:: Clean previous build
if exist %BUILD_DIR% (
    echo Cleaning previous build...
    rmdir /s /q %BUILD_DIR%
)

:: Create build directory
mkdir %BUILD_DIR%
cd %BUILD_DIR%

:: Configure with CMake
echo Configuring with CMake...
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=%CMAKE_BUILD_TYPE% ..

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    goto :error
)

:: Build
echo Building project...
cmake --build . --config %CMAKE_BUILD_TYPE% -j %NUMBER_OF_PROCESSORS%

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    goto :error
)

echo Build completed successfully!
echo Executable location: %BUILD_DIR%\DistributedPluginClient.exe

:: Create directories for runtime
mkdir plugins 2>nul
mkdir logs 2>nul
mkdir temp 2>nul

echo.
echo Usage: DistributedPluginClient.exe [--host <host>] [--port <port>]
echo Default: DistributedPluginClient.exe --host 127.0.0.1 --port 8080
echo.

goto :end

:error
echo Build process failed with error level %ERRORLEVEL%
cd ..
exit /b %ERRORLEVEL%

:end
cd ..
endlocal