#pragma once

#include "common.h"

#ifdef _WIN32
#include <windows.h>
#include <winnt.h>

namespace Loader {

    struct LoadedModule {
        HMODULE base_address;
        size_t size;
        std::string name;
        std::vector<std::string> exports;
        bool is_memory_loaded;
        std::chrono::steady_clock::time_point load_time;
        
        LoadedModule() : base_address(nullptr), size(0), is_memory_loaded(false) {}
    };

    class PELoader {
    public:
        PELoader();
        ~PELoader();

        // Memory DLL loading
        HMODULE LoadFromMemory(const ByteArray& pe_data, const std::string& module_name = "");
        HMODULE LoadFromFile(const std::string& file_path);
        bool UnloadModule(HMODULE module_handle);
        bool UnloadModule(const std::string& module_name);
        
        // Module information
        bool IsModuleLoaded(const std::string& module_name) const;
        HMODULE GetModuleHandle(const std::string& module_name) const;
        std::vector<std::string> GetLoadedModules() const;
        LoadedModule* GetModuleInfo(const std::string& module_name);
        LoadedModule* GetModuleInfo(HMODULE module_handle);
        
        // Function resolution
        FARPROC GetProcAddress(HMODULE module_handle, const std::string& function_name);
        FARPROC GetProcAddress(const std::string& module_name, const std::string& function_name);
        
        // PE analysis
        bool ValidatePE(const ByteArray& pe_data) const;
        bool Is64Bit(const ByteArray& pe_data) const;
        std::vector<std::string> GetExports(const ByteArray& pe_data) const;
        std::vector<std::string> GetImports(const ByteArray& pe_data) const;
        
        // Memory management
        void CleanupAll();
        size_t GetTotalMemoryUsage() const;
        
        // Security features
        bool EnableDEP(HMODULE module_handle);
        bool EnableASLR(HMODULE module_handle);
        void SetSecurityLevel(int level) { security_level_ = level; }
        
    private:
        std::map<std::string, LoadedModule> loaded_modules_;
        std::map<HMODULE, std::string> handle_to_name_;
        mutable std::mutex modules_mutex_;
        int security_level_;
        
        // PE loading implementation
        HMODULE LoadPEFromMemory(const ByteArray& pe_data, const std::string& module_name);
        bool MapSections(const ByteArray& pe_data, LPVOID base_address, PIMAGE_NT_HEADERS nt_headers);
        bool RelocateImage(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers, DWORD_PTR delta);
        bool ResolveImports(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers);
        bool ProtectSections(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers);
        
        // PE parsing helpers
        PIMAGE_DOS_HEADER GetDosHeader(const ByteArray& pe_data) const;
        PIMAGE_NT_HEADERS GetNtHeaders(const ByteArray& pe_data) const;
        PIMAGE_SECTION_HEADER GetSectionHeaders(PIMAGE_NT_HEADERS nt_headers) const;
        PIMAGE_IMPORT_DESCRIPTOR GetImportDescriptor(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers) const;
        PIMAGE_EXPORT_DIRECTORY GetExportDirectory(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers) const;
        
        // Utility functions
        DWORD GetSectionCharacteristics(DWORD characteristics) const;
        bool AllocateMemoryForPE(size_t size, LPVOID preferred_base, LPVOID* allocated_base);
        void FreeAllocatedMemory(LPVOID base_address, size_t size);
        std::string GenerateModuleName() const;
        
        // Import resolution
        HMODULE LoadSystemLibrary(const std::string& library_name);
        FARPROC ResolveImportFunction(const std::string& library_name, const std::string& function_name);
        
        // Security checks
        bool PerformSecurityChecks(const ByteArray& pe_data) const;
        bool CheckDigitalSignature(const ByteArray& pe_data) const;
        bool ScanForMaliciousPatterns(const ByteArray& pe_data) const;
        
        // Error handling
        std::string GetLastErrorString() const;
        void LogPEInfo(const ByteArray& pe_data, const std::string& module_name) const;
    };

    // Custom DLL entry point function type
    typedef BOOL(WINAPI* DLLEntryProc)(HINSTANCE hinstDLL, DWORD fdwReason, LPVOID lpReserved);
    
    // Memory allocation functions for PE loading
    LPVOID AlignedAlloc(size_t size, size_t alignment);
    void AlignedFree(LPVOID ptr);
    
    // PE utility functions
    bool IsPEFile(const ByteArray& data);
    size_t GetPEImageSize(const ByteArray& pe_data);
    std::string GetPEArchitecture(const ByteArray& pe_data);

} // namespace Loader

#endif // _WIN32