const WebSocket = require('ws');
const crypto = require('crypto');

// 简单的UUID生成器
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

class IntegrationTestClient {
    constructor(url = 'ws://localhost:8080') {
        this.url = url;
        this.ws = null;
        this.isConnected = false;
        this.testResults = {
            connection: false,
            heartbeat: false,
            authentication: false,
            systemInfo: false,
            clientList: false,
            pluginList: false
        };
    }

    // XOR加密/解密工具 - 使用与服务器相同的密钥
    xorDecrypt(encryptedBase64) {
        try {
            // 与服务器相同的硬编码密钥
            const cryptoKey = Buffer.from([
                0x4A, 0x7B, 0x9C, 0x2D, 0x8E, 0x5F, 0x31, 0xA6,
                0xB7, 0x48, 0x19, 0xCA, 0x3B, 0xDC, 0x6E, 0x4F,
                0x82, 0x53, 0x94, 0x25, 0xE6, 0x17, 0x78, 0x9A,
                0x1B, 0xFC, 0x2D, 0x8E, 0x5F, 0x60, 0x91, 0xC2
            ]);

            const encryptedBuffer = Buffer.from(encryptedBase64, 'base64');
            const result = Buffer.alloc(encryptedBuffer.length);

            for (let i = 0; i < encryptedBuffer.length; i++) {
                result[i] = encryptedBuffer[i] ^ cryptoKey[i % cryptoKey.length];
            }

            return result.toString('utf8');
        } catch (error) {
            console.error('解密失败:', error);
            return null;
        }
    }

    createMessage(type, data = {}, encrypted = false) {
        const message = {
            type: type,
            id: generateUUID(),
            timestamp: Date.now(),
            encrypted: encrypted,
            version: '1.0',
            data: data
        };

        if (encrypted) {
            // 对于加密消息，我们需要实现加密功能，但目前测试中不需要
            console.log('加密功能暂未实现');
        }

        return message;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.url);

                this.ws.on('open', () => {
                    console.log('✓ 连接到服务器成功');
                    this.isConnected = true;
                    this.testResults.connection = true;
                    resolve();
                });

                this.ws.on('message', (data) => {
                    this.handleMessage(data);
                });

                this.ws.on('close', (code, reason) => {
                    console.log(`连接关闭: ${code} - ${reason}`);
                    this.isConnected = false;
                });

                this.ws.on('error', (error) => {
                    console.error('WebSocket错误:', error);
                    reject(error);
                });

                setTimeout(() => {
                    if (!this.isConnected) {
                        reject(new Error('连接超时'));
                    }
                }, 5000);

            } catch (error) {
                reject(error);
            }
        });
    }

    handleMessage(data) {
        try {
            const message = JSON.parse(data.toString());
            console.log(`收到消息: ${message.type}`);

            // 如果消息是加密的，先解密
            if (message.encrypted && message.data) {
                const decrypted = this.xorDecrypt(message.data);
                if (decrypted) {
                    try {
                        message.data = JSON.parse(decrypted);
                        console.log('  - 消息已解密');
                    } catch (e) {
                        console.log('  - 解密后的数据不是有效JSON:', decrypted.substring(0, 100));
                    }
                }
            }

            switch (message.type) {
                case 'connection_established':
                    console.log('  - 连接已建立，客户端ID:', message.clientId);
                    break;

                case 'heartbeat_response':
                    console.log('✓ 心跳响应正常');
                    this.testResults.heartbeat = true;
                    break;

                case 'auth_success':
                    console.log('✓ 认证成功');
                    this.testResults.authentication = true;
                    break;

                case 'system_info_request_response':
                    console.log('✓ 系统信息获取成功');
                    if (message.data && message.data.systemInfo) {
                        console.log('  - 服务器版本:', message.data.systemInfo.serverVersion);
                        console.log('  - 服务器运行时间:', Math.round(message.data.systemInfo.uptime), '秒');
                        this.testResults.systemInfo = true;
                    } else {
                        console.log('  - 系统信息数据格式异常');
                        console.log('  - 接收到的数据:', JSON.stringify(message.data, null, 2));
                    }
                    break;

                case 'client_list_request_response':
                    console.log('✓ 客户端列表获取成功');
                    console.log('  - 当前客户端数量:', message.data ? message.data.totalCount : 'unknown');
                    this.testResults.clientList = true;
                    break;

                case 'plugin_list_request_response':
                    console.log('✓ 插件列表获取成功');
                    console.log('  - 可用插件数量:', message.data ? message.data.totalCount : 'unknown');
                    this.testResults.pluginList = true;
                    break;

                case 'error':
                    const errorMsg = message.data && message.data.error ? message.data.error.message :
                                   message.error && message.error.message ? message.error.message : '未知错误';
                    console.log('× 收到错误响应:', errorMsg);
                    break;

                default:
                    console.log('  - 未知消息类型:', message.type);
            }

        } catch (error) {
            console.error('解析消息失败:', error);
            console.log('原始数据:', data.toString().substring(0, 200));
        }
    }

    sendMessage(message) {
        if (!this.isConnected) {
            throw new Error('未连接到服务器');
        }

        const messageStr = JSON.stringify(message);
        this.ws.send(messageStr);
        console.log(`发送消息: ${message.type}`);
    }

    async testHeartbeat() {
        console.log('\n=== 测试心跳功能 ===');
        const heartbeatMessage = this.createMessage('heartbeat', {
            timestamp: Date.now(),
            clientTime: new Date().toISOString()
        });

        this.sendMessage(heartbeatMessage);
        await this.sleep(1000);
    }

    async testAuthentication() {
        console.log('\n=== 测试认证功能 ===');
        const authMessage = this.createMessage('auth_request', {
            credentials: {
                username: 'test_client',
                password: 'test_password'
            },
            clientInfo: {
                version: '1.0',
                platform: 'test',
                timestamp: Date.now()
            }
        });

        this.sendMessage(authMessage);
        await this.sleep(1000);
    }

    async testSystemInfo() {
        console.log('\n=== 测试系统信息获取 ===');
        const systemInfoMessage = this.createMessage('system_info_request', {
            requestedInfo: ['os', 'cpu', 'memory', 'disk']
        });

        this.sendMessage(systemInfoMessage);
        await this.sleep(1000);
    }

    async testClientList() {
        console.log('\n=== 测试客户端列表获取 ===');
        const clientListMessage = this.createMessage('client_list_request', {});

        this.sendMessage(clientListMessage);
        await this.sleep(1000);
    }

    async testPluginList() {
        console.log('\n=== 测试插件列表获取 ===');
        const pluginListMessage = this.createMessage('plugin_list_request', {});

        this.sendMessage(pluginListMessage);
        await this.sleep(1000);
    }

    async testEncryptedMessage() {
        console.log('\n=== 测试加密消息 ===');
        const encryptedMessage = this.createMessage('system_info_request', {
            requestedInfo: ['memory']
        }, true);

        this.sendMessage(encryptedMessage);
        await this.sleep(1000);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runAllTests() {
        try {
            console.log('开始集成测试...\n');

            // 连接到服务器
            await this.connect();
            await this.sleep(1000);

            // 执行各项测试
            await this.testHeartbeat();
            await this.testAuthentication();
            await this.testSystemInfo();
            await this.testClientList();
            await this.testPluginList();
            await this.testEncryptedMessage();

            // 等待所有响应
            await this.sleep(2000);

            // 输出测试结果
            this.printTestResults();

        } catch (error) {
            console.error('测试执行失败:', error);
        } finally {
            if (this.ws && this.isConnected) {
                this.ws.close();
            }
        }
    }

    printTestResults() {
        console.log('\n=== 测试结果汇总 ===');
        
        let passedTests = 0;
        let totalTests = 0;

        for (const [testName, result] of Object.entries(this.testResults)) {
            totalTests++;
            const status = result ? '✓ 通过' : '× 失败';
            console.log(`${testName.padEnd(15)}: ${status}`);
            if (result) passedTests++;
        }

        console.log('\n=== 测试统计 ===');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests}`);
        console.log(`失败测试: ${totalTests - passedTests}`);
        console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);

        if (passedTests === totalTests) {
            console.log('\n🎉 所有测试都通过了！核心功能集成成功！');
        } else {
            console.log('\n⚠️  有测试失败，需要检查相关功能实现。');
        }
    }

    disconnect() {
        if (this.ws && this.isConnected) {
            this.ws.close();
        }
    }
}

// 如果直接运行此脚本，执行测试
if (require.main === module) {
    const testClient = new IntegrationTestClient();
    
    testClient.runAllTests().then(() => {
        console.log('\n集成测试完成。');
        process.exit(0);
    }).catch((error) => {
        console.error('集成测试失败:', error);
        process.exit(1);
    });
}

module.exports = IntegrationTestClient;