# Distributed Plugin Management System - C++ Client

企业级分布式插件管理系统的Windows C++客户端实现。

## 功能特性

### 核心功能
- **WebSocket通信**: 基于websocketpp的异步WebSocket客户端
- **XOR加密**: 支持多层加密的数据传输安全
- **内存DLL加载**: PE文件格式解析和内存加载
- **异步插件执行**: 线程池支持的并发插件执行
- **代码混淆**: 编译时字符串加密和反调试保护
- **完整协议支持**: 与Node.js服务器的完整通信协议

### 安全特性
- 编译时字符串混淆
- 运行时反调试检测
- 内存DLL加载(无文件落地)
- 多层XOR加密
- 安全的内存管理

### 性能特性
- 异步IO和多线程架构
- 可配置的线程池大小
- 分块文件传输
- 内存优化的插件加载
- 自动重连机制

## 项目结构

```
client/
├── src/                          # 源代码
│   ├── core/                     # 核心模块
│   │   ├── client/               # 主客户端逻辑
│   │   ├── task_manager/         # 任务管理
│   │   └── plugin_manager/       # 插件管理
│   ├── network/                  # 网络通信
│   │   ├── websocket/            # WebSocket客户端
│   │   └── protocol/             # 通信协议
│   ├── crypto/                   # 加密模块
│   │   ├── xor/                  # XOR加密
│   │   └── encryption/           # 其他加密算法
│   ├── loader/                   # 加载器
│   │   ├── pe_loader/            # PE文件加载器
│   │   └── memory_loader/        # 内存加载器
│   ├── obfuscator/               # 混淆模块
│   │   ├── string_obfuscator/    # 字符串混淆
│   │   └── code_obfuscator/      # 代码混淆
│   ├── plugins/                  # 插件框架
│   │   ├── manager/              # 插件管理器
│   │   └── executor/             # 插件执行器
│   ├── main.cpp                  # 主程序入口
│   └── common.cpp                # 通用工具
├── include/                      # 头文件
├── lib/                          # 第三方库
├── tests/                        # 测试代码
├── docs/                         # 文档
├── CMakeLists.txt               # CMake构建文件
├── build.bat                    # Windows构建脚本
└── README.md                    # 项目说明
```

## 编译和运行

### 环境要求
- Windows 10/11
- MinGW-w64 或 Visual Studio 2019+
- CMake 3.15+
- Git (用于下载依赖)

### 编译步骤

1. **使用构建脚本(推荐)**:
   ```batch
   cd client
   build.bat
   ```

2. **手动编译**:
   ```batch
   mkdir build
   cd build
   cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release ..
   cmake --build . --config Release -j
   ```

### 运行客户端

```batch
# 使用默认配置(连接到localhost:8080)
DistributedPluginClient.exe

# 指定服务器地址和端口
DistributedPluginClient.exe --host ************* --port 8080

# 查看帮助
DistributedPluginClient.exe --help
```

## 依赖库

项目使用以下第三方库(会自动下载):

- **websocketpp**: WebSocket客户端库
- **asio**: 异步IO库
- **nlohmann/json**: JSON解析库
- **OpenSSL**: 加密库(可选)

## 配置

主要配置参数在 `include/config.h` 中:

```cpp
// 服务器配置
constexpr const char* DEFAULT_SERVER_HOST = "127.0.0.1";
constexpr uint16_t DEFAULT_SERVER_PORT = 8080;

// 性能配置
constexpr uint32_t THREAD_POOL_SIZE = 4;
constexpr uint32_t MAX_CONCURRENT_PLUGINS = 16;
constexpr uint32_t CHUNK_SIZE = 64 * 1024; // 64KB

// 安全配置
constexpr uint8_t XOR_KEY[] = {0x42, 0x69, 0x6E, 0x61, 0x72, 0x79, 0x4B, 0x65, 0x79};
```

## API接口

### 插件接口

```cpp
class IPlugin {
public:
    virtual ~IPlugin() = default;
    virtual bool Initialize() = 0;
    virtual bool Execute(const StringMap& params, std::string& result) = 0;
    virtual void Cleanup() = 0;
    virtual std::string GetName() const = 0;
    virtual std::string GetVersion() const = 0;
};
```

### 消息类型

客户端支持以下消息类型:
- `heartbeat`: 心跳检测
- `client_info`: 客户端信息
- `system_info`: 系统信息
- `plugin_load`: 插件加载
- `plugin_unload`: 插件卸载
- `plugin_execute`: 插件执行
- `file_transfer_*`: 文件传输

## 安全说明

### 内存保护
- 使用栈字符串保护敏感数据
- 自动清零内存中的敏感信息
- 支持DEP和ASLR保护

### 反调试
- 编译时字符串混淆
- 运行时调试器检测
- 进程名称检查

### 加密传输
- 所有网络通信默认加密
- 支持多层加密方案
- 密钥动态生成

## 性能优化

### 内存管理
- 智能指针管理资源
- 内存池减少分配开销
- 及时释放不用的插件

### 并发优化
- 线程池复用工作线程
- 异步IO避免阻塞
- 任务队列管理并发

### 网络优化
- 分块传输大文件
- 连接复用减少开销
- 自动重连机制

## 错误处理

客户端实现了完整的错误处理机制:

- 网络连接错误自动重试
- 插件加载失败回滚
- 内存不足时优雅降级
- 异常情况日志记录

## 日志系统

支持多级别日志输出:

```cpp
LOG_DEBUG("调试信息");
LOG_INFO("一般信息");
LOG_WARNING("警告信息");
LOG_ERROR("错误信息");
```

日志同时输出到控制台和文件 `client.log`。

## 故障排除

### 常见问题

1. **编译失败**
   - 检查MinGW或Visual Studio是否正确安装
   - 确保CMake版本 >= 3.15
   - 检查网络连接(需要下载依赖库)

2. **连接失败**
   - 确认服务器地址和端口正确
   - 检查防火墙设置
   - 查看服务器是否正常运行

3. **插件加载失败**
   - 确认插件格式正确(PE文件)
   - 检查插件是否为正确的架构(x64)
   - 查看内存是否充足

### 调试模式

编译调试版本:
```batch
cmake -DCMAKE_BUILD_TYPE=Debug ..
```

## 许可证

本项目仅用于企业内部软件分发和管理，严禁用于恶意用途。

## 联系信息

如有问题或建议，请联系开发团队。