const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

// 插件状态
const PLUGIN_STATUS = {
    AVAILABLE: 'available',
    DISTRIBUTING: 'distributing',
    INSTALLED: 'installed',
    EXECUTING: 'executing',
    FAILED: 'failed',
    DEPRECATED: 'deprecated'
};

// 插件类型
const PLUGIN_TYPES = {
    SYSTEM: 'system',
    FILE_MANAGER: 'file_manager',
    CAMERA: 'camera',
    NETWORK: 'network',
    CUSTOM: 'custom'
};

class PluginManager extends EventEmitter {
    constructor(options = {}) {
        super();
        this.pluginsDir = options.pluginsDir || path.join(__dirname, '../../plugins');
        this.metadataFile = path.join(this.pluginsDir, 'metadata.json');
        this.maxPluginSize = options.maxPluginSize || 50 * 1024 * 1024; // 50MB
        this.supportedFormats = options.supportedFormats || ['.dll', '.exe'];
        
        // 插件存储
        this.plugins = new Map();
        this.pluginVersions = new Map();
        
        // 客户端插件状态跟踪
        this.clientPlugins = new Map();
        
        // 插件分发队列
        this.distributionQueue = new Map();
        
        // 插件执行会话
        this.executionSessions = new Map();
        
        // 统计信息
        this.stats = {
            totalPlugins: 0,
            activeDistributions: 0,
            totalDistributions: 0,
            successfulDistributions: 0,
            failedDistributions: 0,
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0
        };
    }

    // 初始化插件管理器
    async initialize() {
        try {
            // 确保插件目录存在
            await this.ensurePluginsDirectory();
            
            // 加载插件元数据
            await this.loadPluginMetadata();
            
            // 扫描插件目录
            await this.scanPluginsDirectory();
            
            console.log(`插件管理器初始化完成，加载了 ${this.plugins.size} 个插件`);
            this.emit('initialized');
            
        } catch (error) {
            console.error('插件管理器初始化失败:', error);
            throw error;
        }
    }

    // 确保插件目录存在
    async ensurePluginsDirectory() {
        try {
            await fs.access(this.pluginsDir);
        } catch (error) {
            console.log(`创建插件目录: ${this.pluginsDir}`);
            await fs.mkdir(this.pluginsDir, { recursive: true });
        }
    }

    // 加载插件元数据
    async loadPluginMetadata() {
        try {
            const metadataContent = await fs.readFile(this.metadataFile, 'utf8');
            const metadata = JSON.parse(metadataContent);
            
            for (const pluginData of metadata.plugins || []) {
                this.plugins.set(pluginData.name, pluginData);
                
                // 加载版本信息
                if (!this.pluginVersions.has(pluginData.name)) {
                    this.pluginVersions.set(pluginData.name, []);
                }
                this.pluginVersions.get(pluginData.name).push(pluginData.version);
            }
            
            this.stats.totalPlugins = this.plugins.size;
            console.log(`加载插件元数据: ${this.plugins.size} 个插件`);
            
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('元数据文件不存在，将创建新文件');
                await this.savePluginMetadata();
            } else {
                console.warn('加载插件元数据失败:', error.message);
            }
        }
    }

    // 保存插件元数据
    async savePluginMetadata() {
        try {
            const metadata = {
                version: '1.0',
                lastUpdated: new Date().toISOString(),
                plugins: Array.from(this.plugins.values())
            };
            
            await fs.writeFile(this.metadataFile, JSON.stringify(metadata, null, 2));
            console.log('插件元数据已保存');
            
        } catch (error) {
            console.error('保存插件元数据失败:', error);
            throw error;
        }
    }

    // 扫描插件目录
    async scanPluginsDirectory() {
        try {
            const files = await fs.readdir(this.pluginsDir);
            let scannedCount = 0;
            
            for (const file of files) {
                if (file === 'metadata.json') continue;
                
                const filePath = path.join(this.pluginsDir, file);
                const stats = await fs.stat(filePath);
                
                if (stats.isFile() && this.supportedFormats.includes(path.extname(file))) {
                    await this.scanPluginFile(filePath);
                    scannedCount++;
                }
            }
            
            console.log(`扫描插件目录完成: ${scannedCount} 个文件`);
            
        } catch (error) {
            console.error('扫描插件目录失败:', error);
        }
    }

    // 扫描单个插件文件
    async scanPluginFile(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const filename = path.basename(filePath);
            const name = path.parse(filename).name;
            
            // 检查插件是否已存在
            if (this.plugins.has(name)) {
                return;
            }
            
            // 计算文件哈希
            const fileData = await fs.readFile(filePath);
            const hash = crypto.createHash('sha256').update(fileData).digest('hex');
            
            // 创建插件信息
            const plugin = {
                name: name,
                filename: filename,
                version: '1.0.0',
                type: this.detectPluginType(name),
                size: stats.size,
                hash: hash,
                status: PLUGIN_STATUS.AVAILABLE,
                createdAt: stats.ctime.toISOString(),
                updatedAt: stats.mtime.toISOString(),
                description: `插件: ${name}`,
                author: 'System',
                dependencies: [],
                metadata: {
                    architecture: 'x64',
                    minVersion: '1.0.0',
                    permissions: ['basic']
                }
            };
            
            this.plugins.set(name, plugin);
            console.log(`发现新插件: ${name} (${stats.size} 字节)`);
            
        } catch (error) {
            console.error(`扫描插件文件失败 ${filePath}:`, error);
        }
    }

    // 检测插件类型
    detectPluginType(name) {
        const lowerName = name.toLowerCase();
        
        if (lowerName.includes('file') || lowerName.includes('manager')) {
            return PLUGIN_TYPES.FILE_MANAGER;
        } else if (lowerName.includes('camera') || lowerName.includes('video')) {
            return PLUGIN_TYPES.CAMERA;
        } else if (lowerName.includes('network') || lowerName.includes('net')) {
            return PLUGIN_TYPES.NETWORK;
        } else if (lowerName.includes('system') || lowerName.includes('info')) {
            return PLUGIN_TYPES.SYSTEM;
        }
        
        return PLUGIN_TYPES.CUSTOM;
    }

    // 添加插件
    async addPlugin(pluginData, fileBuffer) {
        try {
            // 验证插件数据
            this.validatePluginData(pluginData);
            
            // 检查文件大小
            if (fileBuffer.length > this.maxPluginSize) {
                throw new Error(`插件文件过大: ${fileBuffer.length} > ${this.maxPluginSize}`);
            }
            
            // 检查文件格式
            const ext = path.extname(pluginData.filename);
            if (!this.supportedFormats.includes(ext)) {
                throw new Error(`不支持的文件格式: ${ext}`);
            }
            
            // 计算文件哈希
            const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
            
            // 检查是否已存在相同哈希的插件
            for (const existingPlugin of this.plugins.values()) {
                if (existingPlugin.hash === hash) {
                    throw new Error('插件已存在（相同哈希值）');
                }
            }
            
            // 保存插件文件
            const filePath = path.join(this.pluginsDir, pluginData.filename);
            await fs.writeFile(filePath, fileBuffer);
            
            // 创建插件记录
            const plugin = {
                ...pluginData,
                size: fileBuffer.length,
                hash: hash,
                status: PLUGIN_STATUS.AVAILABLE,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            this.plugins.set(plugin.name, plugin);
            
            // 更新版本信息
            if (!this.pluginVersions.has(plugin.name)) {
                this.pluginVersions.set(plugin.name, []);
            }
            this.pluginVersions.get(plugin.name).push(plugin.version);
            
            // 保存元数据
            await this.savePluginMetadata();
            
            this.stats.totalPlugins++;
            
            console.log(`添加插件成功: ${plugin.name} v${plugin.version}`);
            this.emit('pluginAdded', plugin);
            
            return plugin;
            
        } catch (error) {
            console.error('添加插件失败:', error);
            throw error;
        }
    }

    // 验证插件数据
    validatePluginData(pluginData) {
        const required = ['name', 'filename', 'version', 'type'];
        
        for (const field of required) {
            if (!pluginData[field]) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }
        
        // 验证插件名称
        if (!/^[a-zA-Z0-9_-]+$/.test(pluginData.name)) {
            throw new Error('插件名称只能包含字母、数字、下划线和横线');
        }
        
        // 验证版本格式
        if (!/^\d+\.\d+\.\d+$/.test(pluginData.version)) {
            throw new Error('版本格式必须为 x.y.z');
        }
        
        // 验证插件类型
        if (!Object.values(PLUGIN_TYPES).includes(pluginData.type)) {
            throw new Error(`无效的插件类型: ${pluginData.type}`);
        }
    }

    // 删除插件
    async removePlugin(pluginName) {
        try {
            const plugin = this.plugins.get(pluginName);
            if (!plugin) {
                throw new Error(`插件不存在: ${pluginName}`);
            }
            
            // 删除插件文件
            const filePath = path.join(this.pluginsDir, plugin.filename);
            try {
                await fs.unlink(filePath);
            } catch (error) {
                console.warn(`删除插件文件失败: ${filePath}`, error);
            }
            
            // 从内存中移除
            this.plugins.delete(pluginName);
            this.pluginVersions.delete(pluginName);
            
            // 清理客户端插件状态
            for (const clientPluginMap of this.clientPlugins.values()) {
                clientPluginMap.delete(pluginName);
            }
            
            // 保存元数据
            await this.savePluginMetadata();
            
            this.stats.totalPlugins--;
            
            console.log(`删除插件成功: ${pluginName}`);
            this.emit('pluginRemoved', plugin);
            
            return true;
            
        } catch (error) {
            console.error('删除插件失败:', error);
            throw error;
        }
    }

    // 获取插件信息
    getPlugin(pluginName) {
        return this.plugins.get(pluginName);
    }

    // 获取所有插件
    getAllPlugins() {
        return Array.from(this.plugins.values());
    }

    // 获取插件列表（客户端视图）
    getPluginList(clientId = null) {
        const plugins = Array.from(this.plugins.values()).map(plugin => ({
            name: plugin.name,
            version: plugin.version,
            type: plugin.type,
            size: plugin.size,
            description: plugin.description,
            status: plugin.status,
            metadata: plugin.metadata
        }));
        
        // 如果指定了客户端ID，添加安装状态
        if (clientId && this.clientPlugins.has(clientId)) {
            const clientPluginMap = this.clientPlugins.get(clientId);
            for (const plugin of plugins) {
                const clientPlugin = clientPluginMap.get(plugin.name);
                plugin.installed = !!clientPlugin;
                plugin.installedVersion = clientPlugin ? clientPlugin.version : null;
            }
        }
        
        return plugins;
    }

    // 获取插件文件数据
    async getPluginData(pluginName) {
        try {
            const plugin = this.plugins.get(pluginName);
            if (!plugin) {
                throw new Error(`插件不存在: ${pluginName}`);
            }
            
            const filePath = path.join(this.pluginsDir, plugin.filename);
            const fileData = await fs.readFile(filePath);
            
            // 验证文件完整性
            const hash = crypto.createHash('sha256').update(fileData).digest('hex');
            if (hash !== plugin.hash) {
                throw new Error(`插件文件损坏: ${pluginName}`);
            }
            
            return fileData;
            
        } catch (error) {
            console.error('读取插件数据失败:', error);
            throw error;
        }
    }

    // 分发插件到客户端
    async distributePlugin(pluginName, clientId) {
        try {
            const plugin = this.plugins.get(pluginName);
            if (!plugin) {
                throw new Error(`插件不存在: ${pluginName}`);
            }
            
            const distributionId = uuidv4();
            const distribution = {
                id: distributionId,
                pluginName: pluginName,
                clientId: clientId,
                status: 'pending',
                startTime: Date.now(),
                endTime: null,
                progress: 0,
                error: null
            };
            
            this.distributionQueue.set(distributionId, distribution);
            plugin.status = PLUGIN_STATUS.DISTRIBUTING;
            
            this.stats.activeDistributions++;
            this.stats.totalDistributions++;
            
            console.log(`开始分发插件: ${pluginName} -> ${clientId}`);
            this.emit('distributionStarted', distribution);
            
            return distributionId;
            
        } catch (error) {
            console.error('分发插件失败:', error);
            throw error;
        }
    }

    // 更新分发进度
    updateDistributionProgress(distributionId, progress) {
        const distribution = this.distributionQueue.get(distributionId);
        if (distribution) {
            distribution.progress = progress;
            this.emit('distributionProgress', distribution);
        }
    }

    // 完成插件分发
    completeDistribution(distributionId, success, error = null) {
        const distribution = this.distributionQueue.get(distributionId);
        if (!distribution) {
            return;
        }
        
        distribution.status = success ? 'completed' : 'failed';
        distribution.endTime = Date.now();
        distribution.error = error;
        
        const plugin = this.plugins.get(distribution.pluginName);
        if (plugin) {
            plugin.status = PLUGIN_STATUS.AVAILABLE;
        }
        
        if (success) {
            // 记录客户端插件状态
            if (!this.clientPlugins.has(distribution.clientId)) {
                this.clientPlugins.set(distribution.clientId, new Map());
            }
            
            this.clientPlugins.get(distribution.clientId).set(distribution.pluginName, {
                name: distribution.pluginName,
                version: plugin.version,
                installedAt: new Date().toISOString(),
                status: 'installed'
            });
            
            this.stats.successfulDistributions++;
            console.log(`插件分发成功: ${distribution.pluginName} -> ${distribution.clientId}`);
        } else {
            this.stats.failedDistributions++;
            console.error(`插件分发失败: ${distribution.pluginName} -> ${distribution.clientId}`, error);
        }
        
        this.stats.activeDistributions--;
        this.emit('distributionCompleted', distribution);
        
        // 清理分发记录
        setTimeout(() => {
            this.distributionQueue.delete(distributionId);
        }, 60000); // 1分钟后清理
    }

    // 创建插件执行会话
    createExecutionSession(pluginName, clientId, command, parameters) {
        const sessionId = uuidv4();
        const session = {
            id: sessionId,
            pluginName: pluginName,
            clientId: clientId,
            command: command,
            parameters: parameters,
            status: 'pending',
            startTime: Date.now(),
            endTime: null,
            result: null,
            error: null
        };
        
        this.executionSessions.set(sessionId, session);
        this.stats.totalExecutions++;
        
        console.log(`创建插件执行会话: ${pluginName}.${command} (${sessionId})`);
        this.emit('executionSessionCreated', session);
        
        return sessionId;
    }

    // 更新执行会话状态
    updateExecutionSession(sessionId, status, result = null, error = null) {
        const session = this.executionSessions.get(sessionId);
        if (!session) {
            return false;
        }
        
        session.status = status;
        session.result = result;
        session.error = error;
        
        if (status === 'completed' || status === 'failed') {
            session.endTime = Date.now();
            
            if (status === 'completed') {
                this.stats.successfulExecutions++;
            } else {
                this.stats.failedExecutions++;
            }
        }
        
        this.emit('executionSessionUpdated', session);
        
        // 清理完成的会话
        if (status === 'completed' || status === 'failed') {
            setTimeout(() => {
                this.executionSessions.delete(sessionId);
            }, 300000); // 5分钟后清理
        }
        
        return true;
    }

    // 获取客户端插件状态
    getClientPlugins(clientId) {
        const clientPluginMap = this.clientPlugins.get(clientId);
        if (!clientPluginMap) {
            return [];
        }
        
        return Array.from(clientPluginMap.values());
    }

    // 移除客户端
    removeClient(clientId) {
        this.clientPlugins.delete(clientId);
        console.log(`移除客户端插件状态: ${clientId}`);
    }

    // 获取统计信息
    getStats() {
        return {
            ...this.stats,
            distributionSuccessRate: this.stats.totalDistributions > 0 ? 
                (this.stats.successfulDistributions / this.stats.totalDistributions * 100).toFixed(2) : 0,
            executionSuccessRate: this.stats.totalExecutions > 0 ? 
                (this.stats.successfulExecutions / this.stats.totalExecutions * 100).toFixed(2) : 0,
            averagePluginSize: this.stats.totalPlugins > 0 ? 
                (Array.from(this.plugins.values()).reduce((sum, p) => sum + p.size, 0) / this.stats.totalPlugins).toFixed(2) : 0
        };
    }

    // 清理过期数据
    cleanup() {
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        
        // 清理过期分发记录
        let cleanedDistributions = 0;
        for (const [id, distribution] of this.distributionQueue) {
            if (distribution.endTime && (now - distribution.endTime) > maxAge) {
                this.distributionQueue.delete(id);
                cleanedDistributions++;
            }
        }
        
        // 清理过期执行会话
        let cleanedSessions = 0;
        for (const [id, session] of this.executionSessions) {
            if (session.endTime && (now - session.endTime) > maxAge) {
                this.executionSessions.delete(id);
                cleanedSessions++;
            }
        }
        
        console.log(`清理过期数据: ${cleanedDistributions} 个分发记录, ${cleanedSessions} 个执行会话`);
        
        return { cleanedDistributions, cleanedSessions };
    }
}

module.exports = {
    PluginManager,
    PLUGIN_STATUS,
    PLUGIN_TYPES
};