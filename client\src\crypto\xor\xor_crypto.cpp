#include "xor_crypto.h"
#include <algorithm>
#include <random>
#include <fstream>
#include <iomanip>
#include <sstream>

namespace Crypto {

    XORCrypto::XORCrypto() {
        SetDefaultKey();
    }

    XORCrypto::XORCrypto(const ByteArray& key) : key_(key) {
        if (key_.empty()) {
            SetDefaultKey();
        }
    }

    ByteArray XORCrypto::Encrypt(const ByteArray& data) const {
        if (data.empty() || key_.empty()) {
            return data;
        }
        
        ByteArray result(data.size());
        XOROperation(data.data(), result.data(), data.size());
        return result;
    }

    ByteArray XORCrypto::Decrypt(const ByteArray& data) const {
        // XOR encryption is symmetric
        return Encrypt(data);
    }

    std::string XORCrypto::EncryptString(const std::string& data) const {
        ByteArray bytes(data.begin(), data.end());
        ByteArray encrypted = Encrypt(bytes);
        return std::string(encrypted.begin(), encrypted.end());
    }

    std::string XORCrypto::DecryptString(const std::string& data) const {
        ByteArray bytes(data.begin(), data.end());
        ByteArray decrypted = Decrypt(bytes);
        return std::string(decrypted.begin(), decrypted.end());
    }

    std::string XORCrypto::EncryptToBase64(const ByteArray& data) const {
        ByteArray encrypted = Encrypt(data);
        return Base64Encode(encrypted);
    }

    std::string XORCrypto::EncryptStringToBase64(const std::string& data) const {
        ByteArray bytes(data.begin(), data.end());
        return EncryptToBase64(bytes);
    }

    ByteArray XORCrypto::DecryptFromBase64(const std::string& base64_data) const {
        ByteArray encrypted = Base64Decode(base64_data);
        return Decrypt(encrypted);
    }

    std::string XORCrypto::DecryptStringFromBase64(const std::string& base64_data) const {
        ByteArray decrypted = DecryptFromBase64(base64_data);
        return std::string(decrypted.begin(), decrypted.end());
    }

    bool XORCrypto::EncryptChunked(const ByteArray& input, ByteArray& output, size_t chunk_size) const {
        try {
            output.clear();
            output.reserve(input.size());
            
            for (size_t i = 0; i < input.size(); i += chunk_size) {
                size_t current_chunk_size = std::min(chunk_size, input.size() - i);
                ByteArray chunk(input.begin() + i, input.begin() + i + current_chunk_size);
                ByteArray encrypted_chunk = Encrypt(chunk);
                output.insert(output.end(), encrypted_chunk.begin(), encrypted_chunk.end());
            }
            
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("Chunked encryption failed: " + std::string(e.what()));
            return false;
        }
    }

    bool XORCrypto::DecryptChunked(const ByteArray& input, ByteArray& output, size_t chunk_size) const {
        return EncryptChunked(input, output, chunk_size); // XOR is symmetric
    }

    bool XORCrypto::EncryptFile(const std::string& input_path, const std::string& output_path) const {
        try {
            std::ifstream input_file(input_path, std::ios::binary);
            if (!input_file) {
                LOG_ERROR("Cannot open input file: " + input_path);
                return false;
            }
            
            std::ofstream output_file(output_path, std::ios::binary);
            if (!output_file) {
                LOG_ERROR("Cannot create output file: " + output_path);
                return false;
            }
            
            const size_t buffer_size = Config::CHUNK_SIZE;
            ByteArray buffer(buffer_size);
            
            while (input_file) {
                input_file.read(reinterpret_cast<char*>(buffer.data()), buffer_size);
                std::streamsize bytes_read = input_file.gcount();
                
                if (bytes_read > 0) {
                    buffer.resize(bytes_read);
                    ByteArray encrypted = Encrypt(buffer);
                    output_file.write(reinterpret_cast<const char*>(encrypted.data()), encrypted.size());
                    buffer.resize(buffer_size);
                }
            }
            
            LOG_INFO("File encrypted: " + input_path + " -> " + output_path);
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("File encryption failed: " + std::string(e.what()));
            return false;
        }
    }

    bool XORCrypto::DecryptFile(const std::string& input_path, const std::string& output_path) const {
        return EncryptFile(input_path, output_path); // XOR is symmetric
    }

    void XORCrypto::SetKey(const ByteArray& key) {
        key_ = key;
        if (key_.empty()) {
            SetDefaultKey();
        }
    }

    void XORCrypto::SetKey(const uint8_t* key, size_t key_size) {
        key_.assign(key, key + key_size);
        if (key_.empty()) {
            SetDefaultKey();
        }
    }

    void XORCrypto::SetDefaultKey() {
        key_.assign(Config::XOR_KEY, Config::XOR_KEY + Config::XOR_KEY_SIZE);
    }

    void XORCrypto::GenerateRandomKey(size_t key_size) {
        key_ = GenerateRandomBytes(key_size);
    }

    ByteArray XORCrypto::GenerateRandomBytes(size_t size) {
        ByteArray bytes(size);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);
        
        for (auto& byte : bytes) {
            byte = static_cast<uint8_t>(dis(gen));
        }
        
        return bytes;
    }

    std::string XORCrypto::BytesToHex(const ByteArray& bytes) {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');
        
        for (auto byte : bytes) {
            ss << std::setw(2) << static_cast<unsigned>(byte);
        }
        
        return ss.str();
    }

    ByteArray XORCrypto::HexToBytes(const std::string& hex) {
        ByteArray bytes;
        
        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byte_string = hex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::strtoul(byte_string.c_str(), nullptr, 16));
            bytes.push_back(byte);
        }
        
        return bytes;
    }

    std::string XORCrypto::Base64Encode(const ByteArray& data) {
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int val = 0, valb = -6;
        
        for (auto byte : data) {
            val = (val << 8) + byte;
            valb += 8;
            while (valb >= 0) {
                result.push_back(chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        
        if (valb > -6) {
            result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
        }
        
        while (result.size() % 4) {
            result.push_back('=');
        }
        
        return result;
    }

    ByteArray XORCrypto::Base64Decode(const std::string& base64_data) {
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        ByteArray result;
        int val = 0, valb = -8;
        
        for (auto c : base64_data) {
            if (c == '=') break;
            
            size_t pos = chars.find(c);
            if (pos == std::string::npos) continue;
            
            val = (val << 6) + pos;
            valb += 6;
            
            if (valb >= 0) {
                result.push_back((val >> valb) & 0xFF);
                valb -= 8;
            }
        }
        
        return result;
    }

    ByteArray XORCrypto::EncryptWithSalt(const ByteArray& data) const {
        // Generate random salt
        ByteArray salt = GenerateRandomBytes(16);
        
        // Create enhanced key with salt
        ByteArray enhanced_key = key_;
        enhanced_key.insert(enhanced_key.end(), salt.begin(), salt.end());
        
        // Perform key scheduling
        ByteArray scheduled_key;
        KeySchedule(enhanced_key, scheduled_key);
        
        // Encrypt data
        XORCrypto temp_crypto(scheduled_key);
        ByteArray encrypted_data = temp_crypto.Encrypt(data);
        
        // Prepend salt to encrypted data
        ByteArray result;
        result.insert(result.end(), salt.begin(), salt.end());
        result.insert(result.end(), encrypted_data.begin(), encrypted_data.end());
        
        return result;
    }

    ByteArray XORCrypto::DecryptWithSalt(const ByteArray& encrypted_data) const {
        if (encrypted_data.size() < 16) {
            LOG_ERROR("Invalid encrypted data size");
            return ByteArray();
        }
        
        // Extract salt
        ByteArray salt(encrypted_data.begin(), encrypted_data.begin() + 16);
        ByteArray data(encrypted_data.begin() + 16, encrypted_data.end());
        
        // Create enhanced key with salt
        ByteArray enhanced_key = key_;
        enhanced_key.insert(enhanced_key.end(), salt.begin(), salt.end());
        
        // Perform key scheduling
        ByteArray scheduled_key;
        KeySchedule(enhanced_key, scheduled_key);
        
        // Decrypt data
        XORCrypto temp_crypto(scheduled_key);
        return temp_crypto.Decrypt(data);
    }

    void XORCrypto::XOROperation(const uint8_t* input, uint8_t* output, size_t data_size) const {
        if (key_.empty()) {
            std::copy(input, input + data_size, output);
            return;
        }
        
        for (size_t i = 0; i < data_size; ++i) {
            output[i] = input[i] ^ key_[i % key_.size()];
        }
    }

    void XORCrypto::ExpandKey(size_t target_size) {
        if (key_.size() >= target_size) {
            return;
        }
        
        ByteArray expanded_key;
        expanded_key.reserve(target_size);
        
        while (expanded_key.size() < target_size) {
            expanded_key.insert(expanded_key.end(), key_.begin(), 
                std::min(key_.end(), key_.begin() + (target_size - expanded_key.size())));
        }
        
        key_ = expanded_key;
    }

    ByteArray XORCrypto::GenerateKeyStream(size_t length, uint64_t nonce) const {
        ByteArray stream;
        stream.reserve(length);
        
        // Simple PRNG based on key and nonce
        uint64_t state = nonce;
        for (auto byte : key_) {
            state = (state * 1103515245 + 12345 + byte) & 0xFFFFFFFF;
        }
        
        for (size_t i = 0; i < length; ++i) {
            state = (state * 1103515245 + 12345) & 0xFFFFFFFF;
            stream.push_back(static_cast<uint8_t>(state >> 24));
        }
        
        return stream;
    }

    void XORCrypto::KeySchedule(const ByteArray& original_key, ByteArray& scheduled_key, size_t rounds) const {
        scheduled_key = original_key;
        
        for (size_t round = 0; round < rounds; ++round) {
            // Simple key scheduling algorithm
            for (size_t i = 0; i < scheduled_key.size(); ++i) {
                uint8_t temp = scheduled_key[i];
                temp = ((temp << 1) | (temp >> 7)) & 0xFF; // Rotate left
                temp ^= static_cast<uint8_t>(round + i);
                scheduled_key[i] = temp;
            }
        }
    }

} // namespace Crypto