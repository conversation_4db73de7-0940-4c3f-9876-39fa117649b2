cmake_minimum_required(VERSION 3.15)
project(DistributedPluginClient VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

set(CMAKE_CXX_FLAGS "-Wall -Wextra")
set(CMAKE_CXX_FLAGS_DEBUG "-g")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Include directories
include_directories(include)
include_directories(src)

# Find required packages
find_package(Threads REQUIRED)

# OpenSSL for secure connections (optional)
find_package(OpenSSL QUIET)
if(OPENSSL_FOUND)
    include_directories(${OPENSSL_INCLUDE_DIR})
    message(STATUS "OpenSSL found - SSL support enabled")
else()
    message(STATUS "OpenSSL not found - SSL support disabled")
endif()

# Note: websocketpp and asio are no longer needed as we use SimpleWebSocketClient

# nlohmann/json for JSON handling
find_path(JSON_INCLUDE_DIR
    NAMES nlohmann/json.hpp
    PATHS ${CMAKE_CURRENT_SOURCE_DIR}/lib/json/include
)

if(NOT JSON_INCLUDE_DIR)
    message(STATUS "nlohmann/json not found, creating lib directory...")
    file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    set(JSON_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib/json/include)
    message(STATUS "Please download nlohmann/json manually to: ${CMAKE_CURRENT_SOURCE_DIR}/lib/json")
endif()

include_directories(${JSON_INCLUDE_DIR})

# Source files (exclude WebSocket implementations from glob)
file(GLOB_RECURSE SOURCES
    "src/crypto/*.cpp"
    "src/loader/*.cpp"
    "src/obfuscator/*.cpp"
    "src/plugins/*.cpp"
)

# Add network files except WebSocket implementations
file(GLOB_RECURSE NETWORK_SOURCES
    "src/network/*.cpp"
)

# Remove WebSocket implementation files from network sources
list(FILTER NETWORK_SOURCES EXCLUDE REGEX ".*simple_websocket_client.*\\.cpp$")
list(APPEND SOURCES ${NETWORK_SOURCES})

# Add common.cpp separately
list(APPEND SOURCES "src/common.cpp")

# Add SimpleWebSocketClient implementation (choose based on OpenSSL availability)
if(OPENSSL_FOUND)
    list(APPEND SOURCES "src/network/websocket/simple_websocket_client.cpp")
    message(STATUS "Using WebSocket client with OpenSSL support")
else()
    list(APPEND SOURCES "src/network/websocket/simple_websocket_client_no_ssl.cpp")
    message(STATUS "Using WebSocket client without OpenSSL support")
endif()

# Headers
file(GLOB_RECURSE HEADERS
    "include/*.h"
    "src/network/*.h"
    "src/crypto/*.h"
    "src/loader/*.h"
    "src/obfuscator/*.h"
    "src/plugins/*.h"
)

# Main executable
add_executable(${PROJECT_NAME} src/main.cpp ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    Threads::Threads
)

# Link OpenSSL if available
if(OPENSSL_FOUND)
    target_link_libraries(${PROJECT_NAME} ${OPENSSL_LIBRARIES})
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_OPENSSL)
endif()

# Windows specific libraries
if(WIN32)
    target_link_libraries(${PROJECT_NAME}
        ws2_32
        wsock32
        kernel32
        user32
        gdi32
        winspool
        comdlg32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        odbc32
        odbccp32
    )
endif()

# Preprocessor definitions
target_compile_definitions(${PROJECT_NAME} PRIVATE
    _WIN32_WINNT=0x0601
)

# Build optimization
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG)
    if(WIN32)
        if(MSVC)
            set_target_properties(${PROJECT_NAME} PROPERTIES
                WIN32_EXECUTABLE TRUE
                LINK_FLAGS "/SUBSYSTEM:CONSOLE /OPT:REF /OPT:ICF"
            )
        else()
            # MinGW flags
            set_target_properties(${PROJECT_NAME} PROPERTIES
                LINK_FLAGS "-Wl,--subsystem,console -Wl,--gc-sections"
            )
        endif()
    endif()
endif()

# Tests (optional)
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()