#pragma once

#include "common.h"
#include "config.h"

// Use our simplified WebSocket implementation
#include "simple_websocket_client.h"

namespace Network {

    class WebSocketClient {
    public:
        // Use our simplified WebSocket client
        using ConnectionState = SimpleWebSocketClient::ConnectionState;
        using MessageCallback = SimpleWebSocketClient::MessageCallback;
        using StateCallback = SimpleWebSocketClient::StateCallback;

        WebSocketClient();
        ~WebSocketClient();

        bool Connect(const std::string& host, uint16_t port, const std::string& path = "/");
        void Disconnect();
        bool Send(const std::string& message);
        bool SendBinary(const ByteArray& data);
        
        void SetMessageCallback(MessageCallback callback);
        void SetStateCallback(StateCallback callback);
        
        ConnectionState GetState() const;
        bool IsConnected() const;
        
        void StartReconnectLoop();
        void StopReconnectLoop();
        
        void Run();
        void Stop();
        
        // Set server parameters for auto-reconnect
        void SetServerParams(const std::string& host, uint16_t port, const std::string& path = "/");

    private:
        std::unique_ptr<SimpleWebSocketClient> client_;
    };

} // namespace Network