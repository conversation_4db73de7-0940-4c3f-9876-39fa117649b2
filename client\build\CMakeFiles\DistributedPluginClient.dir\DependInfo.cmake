
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/Users/<USER>/Desktop/windows/client/src/common.cpp" "CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/crypto/xor/xor_crypto.cpp" "CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/loader/pe_loader/pe_loader.cpp" "CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/main.cpp" "CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/network/protocol/protocol_handler.cpp" "CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/network/websocket/simple_websocket_client_no_ssl.cpp" "CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/network/websocket/websocket_client.cpp" "CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/obfuscator/string_obfuscator/string_obfuscator.cpp" "CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj.d"
  "C:/Users/<USER>/Desktop/windows/client/src/plugins/executor/plugin_executor.cpp" "CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj" "gcc" "CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
