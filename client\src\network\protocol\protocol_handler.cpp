#include "protocol_handler.h"
#include "obfuscator/string_obfuscator/string_obfuscator.h"
#include <random>
#include <sstream>
#include <iomanip>

namespace Network {

    ProtocolHandler::ProtocolHandler()
        : sequence_counter_(0)
        , encryption_enabled_(true)
        , compression_enabled_(false)
        , messages_sent_(0)
        , messages_received_(0)
        , bytes_transferred_(0) {
        
        crypto_ = std::make_unique<Crypto::XORCrypto>();
        LOG_INFO("Protocol handler initialized");
    }

    bool ProtocolHandler::SendMessage(const ProtocolMessage& message) {
        if (!send_function_) {
            LOG_ERROR("Send function not set");
            return false;
        }
        
        if (!ValidateMessage(message)) {
            LOG_ERROR("Message validation failed");
            return false;
        }
        
        try {
            std::string serialized = SerializeMessage(message);
            if (serialized.empty()) {
                LOG_ERROR("Failed to serialize message");
                return false;
            }
            
            bool success = send_function_(serialized);
            if (success) {
                messages_sent_++;
                bytes_transferred_ += serialized.length();
            }
            
            return success;
            
        } catch (const std::exception& e) {
            LOG_ERROR("Exception sending message: " + std::string(e.what()));
            return false;
        }
    }

    bool ProtocolHandler::ParseMessage(const std::string& raw_message, ProtocolMessage& message) {
        try {
            if (raw_message.empty()) {
                return false;
            }
            
            nlohmann::json json_msg = nlohmann::json::parse(raw_message);
            
            // Validate required fields
            if (!json_msg.contains(Protocol::FIELD_TYPE) || 
                !json_msg.contains(Protocol::FIELD_ID)) {
                LOG_ERROR("Missing required message fields");
                return false;
            }
            
            // Parse message fields
            std::string type_str = std::string(json_msg[Protocol::FIELD_TYPE]);
            message.type = StringToMessageType(type_str);
            message.id = std::string(json_msg[Protocol::FIELD_ID]);
            message.client_id = json_msg.value(Protocol::FIELD_CLIENT_ID, "");
            message.target = json_msg.value(Protocol::FIELD_TARGET, "");
            message.encrypted = json_msg.value(Protocol::FIELD_ENCRYPTED, true);
            message.timestamp = json_msg.value(Protocol::FIELD_TIMESTAMP, GetCurrentTimestamp());
            message.sequence_number = json_msg.value(Protocol::FIELD_SEQUENCE, 0);
            
            // Parse payload
            if (json_msg.contains(Protocol::FIELD_PAYLOAD)) {
                if (message.encrypted && encryption_enabled_) {
                    std::string encrypted_payload = std::string(json_msg[Protocol::FIELD_PAYLOAD]);
                    std::string decrypted_payload = DecryptPayload(encrypted_payload);
                    message.payload = nlohmann::json::parse(decrypted_payload);
                } else {
                    message.payload = json_msg[Protocol::FIELD_PAYLOAD];
                }
            }
            
            // Parse metadata
            if (json_msg.contains(Protocol::FIELD_METADATA)) {
                // For stub mode, skip metadata parsing
                // In real implementation this would parse the JSON metadata object
            }
            
            messages_received_++;
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("Exception parsing message: " + std::string(e.what()));
            return false;
        }
    }

    std::string ProtocolHandler::SerializeMessage(const ProtocolMessage& message) {
        try {
            nlohmann::json json_msg;
            
            // Required fields
            json_msg[Protocol::FIELD_TYPE] = MessageTypeToString(message.type);
            json_msg[Protocol::FIELD_ID] = message.id;
            json_msg[Protocol::FIELD_VERSION] = Protocol::VERSION;
            
            // Optional fields
            if (!message.client_id.empty()) {
                json_msg[Protocol::FIELD_CLIENT_ID] = message.client_id;
            }
            
            if (!message.target.empty()) {
                json_msg[Protocol::FIELD_TARGET] = message.target;
            }
            
            json_msg[Protocol::FIELD_ENCRYPTED] = message.encrypted;
            json_msg[Protocol::FIELD_TIMESTAMP] = message.timestamp > 0 ? message.timestamp : GetCurrentTimestamp();
            json_msg[Protocol::FIELD_SEQUENCE] = message.sequence_number > 0 ? message.sequence_number : ++sequence_counter_;
            
            // Payload
            if (!message.payload.empty()) {
                if (message.encrypted && encryption_enabled_) {
                    std::string payload_str = message.payload.dump();
                    std::string encrypted_payload = EncryptPayload(payload_str);
                    json_msg[Protocol::FIELD_PAYLOAD] = encrypted_payload;
                } else {
                    json_msg[Protocol::FIELD_PAYLOAD] = message.payload;
                }
            }
            
            // Metadata
            if (!message.metadata.empty()) {
                nlohmann::json metadata_json;
                for (const auto& item : message.metadata) {
                    metadata_json[item.first] = item.second;
                }
                json_msg[Protocol::FIELD_METADATA] = metadata_json;
            }
            
            return json_msg.dump();
            
        } catch (const std::exception& e) {
            LOG_ERROR("Exception serializing message: " + std::string(e.what()));
            return "";
        }
    }

    ProtocolMessage ProtocolHandler::CreateHeartbeat() {
        ProtocolMessage message;
        message.type = MessageType::HEARTBEAT;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        message.timestamp = GetCurrentTimestamp();
        
        message.payload["timestamp"] = message.timestamp;
        message.payload["client_id"] = client_id_;
        
        return message;
    }

    ProtocolMessage ProtocolHandler::CreateClientInfo() {
        ProtocolMessage message;
        message.type = MessageType::CLIENT_INFO;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        
        message.payload["client_type"] = Config::CLIENT_TYPE;
        message.payload["protocol_version"] = Config::PROTOCOL_VERSION;
        // Simplified for stub - in real implementation would create proper JSON array
        message.payload["capabilities"] = "plugin_loading,memory_dll_loading,encryption,chunked_transfer,compression";
        
#ifdef _WIN32
        message.payload["platform"] = "Windows";
        message.payload["architecture"] = "x64";
#else
        message.payload["platform"] = "Unknown";
        message.payload["architecture"] = "Unknown";
#endif
        
        return message;
    }

    ProtocolMessage ProtocolHandler::CreateSystemInfo() {
        ProtocolMessage message;
        message.type = MessageType::SYSTEM_INFO_RESPONSE;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        
#ifdef _WIN32
        SYSTEM_INFO si;
        GetSystemInfo(&si);
        
        MEMORYSTATUSEX ms;
        ms.dwLength = sizeof(ms);
        GlobalMemoryStatusEx(&ms);
        
        message.payload["os"] = "Windows";
        message.payload["processors"] = si.dwNumberOfProcessors;
        message.payload["total_memory"] = ms.ullTotalPhys;
        message.payload["available_memory"] = ms.ullAvailPhys;
        
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
            message.payload["process_memory"] = pmc.WorkingSetSize;
        }
#endif
        
        return message;
    }

    ProtocolMessage ProtocolHandler::CreatePluginLoadResponse(const std::string& plugin_name, bool success, const std::string& error) {
        ProtocolMessage message;
        message.type = MessageType::PLUGIN_LOAD_RESPONSE;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        
        message.payload["plugin_name"] = plugin_name;
        message.payload["success"] = success;
        if (!success && !error.empty()) {
            message.payload["error"] = error;
        }
        
        return message;
    }

    ProtocolMessage ProtocolHandler::CreatePluginExecuteResponse(const std::string& task_id, bool success, const std::string& result, const std::string& error) {
        ProtocolMessage message;
        message.type = MessageType::PLUGIN_EXECUTE_RESPONSE;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        
        message.payload["task_id"] = task_id;
        message.payload["success"] = success;
        
        if (success) {
            message.payload["result"] = result;
        } else if (!error.empty()) {
            message.payload["error"] = error;
        }
        
        return message;
    }

    ProtocolMessage ProtocolHandler::CreateError(const std::string& error_message, const std::string& error_code) {
        ProtocolMessage message;
        message.type = MessageType::ERROR;
        message.id = GenerateMessageId();
        message.client_id = client_id_;
        
        message.payload["error_message"] = error_message;
        if (!error_code.empty()) {
            message.payload["error_code"] = error_code;
        }
        message.payload["timestamp"] = GetCurrentTimestamp();
        
        return message;
    }

    bool ProtocolHandler::StartFileTransfer(const std::string& transfer_id, const std::string& filename, const ByteArray& data) {
        std::lock_guard<std::mutex> lock(transfers_mutex_);
        
        if (active_transfers_.find(transfer_id) != active_transfers_.end()) {
            LOG_WARNING("Transfer already exists: " + transfer_id);
            return false;
        }
        
        FileTransferInfo transfer_info;
        transfer_info.transfer_id = transfer_id;
        transfer_info.filename = filename;
        transfer_info.total_size = data.size();
        transfer_info.chunk_size = Protocol::CHUNK_SIZE;
        transfer_info.chunks_total = (data.size() + Protocol::CHUNK_SIZE - 1) / Protocol::CHUNK_SIZE;
        transfer_info.chunks_received = 0;
        transfer_info.data = data;
        transfer_info.start_time = std::chrono::steady_clock::now();
        
        active_transfers_[transfer_id] = transfer_info;
        
        // Send start message
        ProtocolMessage start_msg;
        start_msg.type = MessageType::FILE_TRANSFER_START;
        start_msg.id = GenerateMessageId();
        start_msg.client_id = client_id_;
        
        start_msg.payload["transfer_id"] = transfer_id;
        start_msg.payload["filename"] = filename;
        start_msg.payload["total_size"] = data.size();
        start_msg.payload["chunk_size"] = Protocol::CHUNK_SIZE;
        start_msg.payload["chunks_total"] = transfer_info.chunks_total;
        
        bool success = SendMessage(start_msg);
        if (!success) {
            active_transfers_.erase(transfer_id);
            return false;
        }
        
        // Send chunks
        auto chunks = SplitIntoChunks(data, Protocol::CHUNK_SIZE);
        for (size_t i = 0; i < chunks.size(); ++i) {
            if (!SendFileChunk(transfer_id, i, chunks[i])) {
                LOG_ERROR("Failed to send chunk " + std::to_string(i) + " for transfer " + transfer_id);
                active_transfers_.erase(transfer_id);
                return false;
            }
        }
        
        // Send end message
        ProtocolMessage end_msg;
        end_msg.type = MessageType::FILE_TRANSFER_END;
        end_msg.id = GenerateMessageId();
        end_msg.client_id = client_id_;
        
        end_msg.payload["transfer_id"] = transfer_id;
        end_msg.payload["success"] = true;
        
        success = SendMessage(end_msg);
        if (success) {
            active_transfers_[transfer_id].completed = true;
        }
        
        return success;
    }

    bool ProtocolHandler::HandleFileTransferChunk(const ProtocolMessage& message) {
        std::lock_guard<std::mutex> lock(transfers_mutex_);
        
        if (!message.payload.contains("transfer_id") || !message.payload.contains("chunk_index") || !message.payload.contains("chunk_data")) {
            LOG_ERROR("Invalid file transfer chunk message");
            return false;
        }
        
        std::string transfer_id = std::string(message.payload["transfer_id"]);
        size_t chunk_index = size_t(message.payload["chunk_index"]);
        std::string chunk_data_b64 = std::string(message.payload["chunk_data"]);
        
        auto it = active_transfers_.find(transfer_id);
        if (it == active_transfers_.end()) {
            LOG_ERROR("Unknown transfer ID: " + transfer_id);
            return false;
        }
        
        FileTransferInfo& transfer_info = it->second;
        
        // Decode chunk data
        ByteArray chunk_data = Crypto::XORCrypto::Base64Decode(chunk_data_b64);
        
        // Store chunk (simplified - in real implementation you'd handle out-of-order chunks)
        size_t offset = chunk_index * transfer_info.chunk_size;
        if (offset + chunk_data.size() <= transfer_info.total_size) {
            std::copy(chunk_data.begin(), chunk_data.end(), transfer_info.data.begin() + offset);
            transfer_info.chunks_received++;
        }
        
        return true;
    }

    bool ProtocolHandler::CompleteFileTransfer(const std::string& transfer_id, ByteArray& data) {
        std::lock_guard<std::mutex> lock(transfers_mutex_);
        
        auto it = active_transfers_.find(transfer_id);
        if (it == active_transfers_.end()) {
            return false;
        }
        
        FileTransferInfo& transfer_info = it->second;
        if (!transfer_info.completed) {
            return false;
        }
        
        data = transfer_info.data;
        active_transfers_.erase(it);
        
        return true;
    }

    std::vector<std::string> ProtocolHandler::GetActiveTransfers() {
        std::lock_guard<std::mutex> lock(transfers_mutex_);
        
        std::vector<std::string> transfers;
        for (const auto& transfer : active_transfers_) {
            transfers.push_back(transfer.first);
        }
        
        return transfers;
    }

    bool ProtocolHandler::ValidateMessage(const ProtocolMessage& message) {
        if (message.type == MessageType::UNKNOWN) {
            return false;
        }
        
        if (message.id.empty()) {
            return false;
        }
        
        return ValidatePayload(message.type, message.payload);
    }

    bool ProtocolHandler::ValidatePayload(MessageType type, const nlohmann::json& payload) {
        switch (type) {
            case MessageType::HEARTBEAT:
                return Schema::ValidateHeartbeat(payload);
            case MessageType::CLIENT_INFO:
                return Schema::ValidateClientInfo(payload);
            case MessageType::SYSTEM_INFO_RESPONSE:
                return Schema::ValidateSystemInfo(payload);
            case MessageType::PLUGIN_LOAD:
                return Schema::ValidatePluginLoad(payload);
            case MessageType::PLUGIN_EXECUTE:
                return Schema::ValidatePluginExecute(payload);
            case MessageType::FILE_TRANSFER_START:
            case MessageType::FILE_TRANSFER_CHUNK:
            case MessageType::FILE_TRANSFER_END:
                return Schema::ValidateFileTransfer(payload);
            case MessageType::TASK_CREATE:
                return Schema::ValidateTaskCreate(payload);
            default:
                return true; // Unknown types are allowed
        }
    }

    // Private methods implementation

    std::string ProtocolHandler::EncryptPayload(const std::string& payload) {
        if (!encryption_enabled_ || !crypto_) {
            return payload;
        }
        
        return crypto_->EncryptStringToBase64(payload);
    }

    std::string ProtocolHandler::DecryptPayload(const std::string& encrypted_payload) {
        if (!encryption_enabled_ || !crypto_) {
            return encrypted_payload;
        }
        
        return crypto_->DecryptStringFromBase64(encrypted_payload);
    }

    std::string ProtocolHandler::CompressData(const std::string& data) {
        // Placeholder for compression implementation
        return data;
    }

    std::string ProtocolHandler::DecompressData(const std::string& compressed_data) {
        // Placeholder for decompression implementation
        return compressed_data;
    }

    std::string ProtocolHandler::GenerateMessageId() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);
        
        std::stringstream ss;
        ss << "msg_";
        for (int i = 0; i < 16; ++i) {
            ss << std::hex << dis(gen);
        }
        
        return ss.str();
    }

    uint64_t ProtocolHandler::GetCurrentTimestamp() {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    MessageType ProtocolHandler::StringToMessageType(const std::string& type_str) {
        static const std::map<std::string, MessageType> type_map = {
            {"connect", MessageType::CONNECT},
            {"disconnect", MessageType::DISCONNECT},
            {"heartbeat", MessageType::HEARTBEAT},
            {"heartbeat_response", MessageType::HEARTBEAT_RESPONSE},
            {"client_info", MessageType::CLIENT_INFO},
            {"client_info_response", MessageType::CLIENT_INFO_RESPONSE},
            {"system_info", MessageType::SYSTEM_INFO},
            {"system_info_response", MessageType::SYSTEM_INFO_RESPONSE},
            {"plugin_list", MessageType::PLUGIN_LIST},
            {"plugin_list_response", MessageType::PLUGIN_LIST_RESPONSE},
            {"plugin_load", MessageType::PLUGIN_LOAD},
            {"plugin_load_response", MessageType::PLUGIN_LOAD_RESPONSE},
            {"plugin_unload", MessageType::PLUGIN_UNLOAD},
            {"plugin_unload_response", MessageType::PLUGIN_UNLOAD_RESPONSE},
            {"plugin_execute", MessageType::PLUGIN_EXECUTE},
            {"plugin_execute_response", MessageType::PLUGIN_EXECUTE_RESPONSE},
            {"file_transfer_start", MessageType::FILE_TRANSFER_START},
            {"file_transfer_chunk", MessageType::FILE_TRANSFER_CHUNK},
            {"file_transfer_end", MessageType::FILE_TRANSFER_END},
            {"file_transfer_status", MessageType::FILE_TRANSFER_STATUS},
            {"task_create", MessageType::TASK_CREATE},
            {"task_status", MessageType::TASK_STATUS},
            {"task_result", MessageType::TASK_RESULT},
            {"task_cancel", MessageType::TASK_CANCEL},
            {"error", MessageType::ERROR},
            {"custom", MessageType::CUSTOM}
        };
        
        auto it = type_map.find(type_str);
        return (it != type_map.end()) ? it->second : MessageType::UNKNOWN;
    }

    std::string ProtocolHandler::MessageTypeToString(MessageType type) {
        switch (type) {
            case MessageType::CONNECT: return "connect";
            case MessageType::DISCONNECT: return "disconnect";
            case MessageType::HEARTBEAT: return "heartbeat";
            case MessageType::HEARTBEAT_RESPONSE: return "heartbeat_response";
            case MessageType::CLIENT_INFO: return "client_info";
            case MessageType::CLIENT_INFO_RESPONSE: return "client_info_response";
            case MessageType::SYSTEM_INFO: return "system_info";
            case MessageType::SYSTEM_INFO_RESPONSE: return "system_info_response";
            case MessageType::PLUGIN_LIST: return "plugin_list";
            case MessageType::PLUGIN_LIST_RESPONSE: return "plugin_list_response";
            case MessageType::PLUGIN_LOAD: return "plugin_load";
            case MessageType::PLUGIN_LOAD_RESPONSE: return "plugin_load_response";
            case MessageType::PLUGIN_UNLOAD: return "plugin_unload";
            case MessageType::PLUGIN_UNLOAD_RESPONSE: return "plugin_unload_response";
            case MessageType::PLUGIN_EXECUTE: return "plugin_execute";
            case MessageType::PLUGIN_EXECUTE_RESPONSE: return "plugin_execute_response";
            case MessageType::FILE_TRANSFER_START: return "file_transfer_start";
            case MessageType::FILE_TRANSFER_CHUNK: return "file_transfer_chunk";
            case MessageType::FILE_TRANSFER_END: return "file_transfer_end";
            case MessageType::FILE_TRANSFER_STATUS: return "file_transfer_status";
            case MessageType::TASK_CREATE: return "task_create";
            case MessageType::TASK_STATUS: return "task_status";
            case MessageType::TASK_RESULT: return "task_result";
            case MessageType::TASK_CANCEL: return "task_cancel";
            case MessageType::ERROR: return "error";
            case MessageType::CUSTOM: return "custom";
            default: return "unknown";
        }
    }

    std::vector<ByteArray> ProtocolHandler::SplitIntoChunks(const ByteArray& data, size_t chunk_size) {
        std::vector<ByteArray> chunks;
        
        for (size_t i = 0; i < data.size(); i += chunk_size) {
            size_t current_chunk_size = std::min(chunk_size, data.size() - i);
            ByteArray chunk(data.begin() + i, data.begin() + i + current_chunk_size);
            chunks.push_back(chunk);
        }
        
        return chunks;
    }

    bool ProtocolHandler::SendFileChunk(const std::string& transfer_id, size_t chunk_index, const ByteArray& chunk) {
        ProtocolMessage chunk_msg;
        chunk_msg.type = MessageType::FILE_TRANSFER_CHUNK;
        chunk_msg.id = GenerateMessageId();
        chunk_msg.client_id = client_id_;
        
        chunk_msg.payload["transfer_id"] = transfer_id;
        chunk_msg.payload["chunk_index"] = chunk_index;
        chunk_msg.payload["chunk_data"] = Crypto::XORCrypto::Base64Encode(chunk);
        
        return SendMessage(chunk_msg);
    }

    // Schema validation implementations
    namespace Schema {
        bool ValidateHeartbeat(const nlohmann::json& payload) {
            return payload.contains("timestamp");
        }
        
        bool ValidateClientInfo(const nlohmann::json& payload) {
            return payload.contains("client_type") && payload.contains("protocol_version");
        }
        
        bool ValidateSystemInfo(const nlohmann::json& payload) {
            return true; // System info can vary
        }
        
        bool ValidatePluginLoad(const nlohmann::json& payload) {
            return payload.contains("plugin_name") && payload.contains("plugin_data");
        }
        
        bool ValidatePluginExecute(const nlohmann::json& payload) {
            return payload.contains("plugin_name") && payload.contains("task_id");
        }
        
        bool ValidateFileTransfer(const nlohmann::json& payload) {
            return payload.contains("transfer_id");
        }
        
        bool ValidateTaskCreate(const nlohmann::json& payload) {
            return payload.contains("task_type") && payload.contains("task_id");
        }
    }

} // namespace Network