#include "plugin_executor.h"
#include <random>

namespace Plugins {

    // ThreadPool implementation
    ThreadPool::ThreadPool(size_t thread_count) : stop_(false), active_threads_(0) {
        for (size_t i = 0; i < thread_count; ++i) {
            workers_.emplace_back([this] {
                while (true) {
                    std::function<void()> task;
                    
                    {
                        std::unique_lock<std::mutex> lock(queue_mutex_);
                        condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
                        
                        if (stop_ && tasks_.empty()) {
                            return;
                        }
                        
                        task = std::move(tasks_.front());
                        tasks_.pop();
                    }
                    
                    active_threads_++;
                    try {
                        task();
                    } catch (const std::exception& e) {
                        LOG_ERROR("Thread pool task exception: " + std::string(e.what()));
                    }
                    active_threads_--;
                }
            });
        }
        
        LOG_INFO("Thread pool initialized with " + std::to_string(thread_count) + " threads");
    }

    ThreadPool::~ThreadPool() {
        Shutdown();
    }

    void ThreadPool::Shutdown() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            stop_ = true;
        }
        
        condition_.notify_all();
        
        for (std::thread& worker : workers_) {
            if (worker.joinable()) {
                worker.join();
            }
        }
        
        workers_.clear();
        
        // Clear remaining tasks
        std::queue<std::function<void()>> empty;
        tasks_.swap(empty);
    }

    size_t ThreadPool::GetQueueSize() const {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queue_mutex_));
        return tasks_.size();
    }

    // PluginExecutor implementation
    PluginExecutor::PluginExecutor()
        : max_concurrent_tasks_(Config::MAX_CONCURRENT_PLUGINS)
        , completed_tasks_(0)
        , failed_tasks_(0)
        , default_timeout_(std::chrono::minutes(5))
        , total_execution_time_(0) {
        
        thread_pool_ = std::make_unique<ThreadPool>(Config::THREAD_POOL_SIZE);
        LOG_INFO("Plugin executor initialized");
    }

    PluginExecutor::~PluginExecutor() {
        Shutdown();
    }

    std::string PluginExecutor::SubmitTask(const std::string& plugin_name,
                                          const std::string& function_name,
                                          const StringMap& parameters,
                                          TaskCallback callback,
                                          uint32_t priority,
                                          std::chrono::milliseconds timeout) {
        
        if (IsTaskLimitReached()) {
            LOG_WARNING("Task limit reached, rejecting new task");
            if (callback) {
                callback(false, "Task limit reached");
            }
            return "";
        }
        
        auto task = std::make_shared<PluginTask>();
        task->task_id = GenerateTaskId();
        task->plugin_name = plugin_name;
        task->function_name = function_name;
        task->parameters = parameters;
        task->priority = priority;
        task->timeout = timeout;
        task->callback = callback;
        
        {
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            tasks_[task->task_id] = task;
        }
        
        // Submit to thread pool
        thread_pool_->Submit([this, task]() {
            ExecuteTask(task);
        });
        
        LOG_INFO("Task submitted: " + task->task_id + " for plugin: " + plugin_name);
        return task->task_id;
    }

    bool PluginExecutor::CancelTask(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        
        auto it = tasks_.find(task_id);
        if (it == tasks_.end()) {
            return false;
        }
        
        auto task = it->second;
        
        // Mark task as cancelled
        ExecutionResult result;
        result.status = TaskStatus::CANCELLED;
        result.error_message = "Task cancelled by user";
        
        try {
            task->promise.set_value(result);
        } catch (const std::exception&) {
            // Promise may already be set
        }
        
        if (task->callback) {
            task->callback(false, "Task cancelled");
        }
        
        tasks_.erase(it);
        LOG_INFO("Task cancelled: " + task_id);
        return true;
    }

    TaskStatus PluginExecutor::GetTaskStatus(const std::string& task_id) {
        {
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            auto it = tasks_.find(task_id);
            if (it != tasks_.end()) {
                return TaskStatus::RUNNING;
            }
        }
        
        {
            std::lock_guard<std::mutex> lock(results_mutex_);
            auto it = completed_results_.find(task_id);
            if (it != completed_results_.end()) {
                return it->second.status;
            }
        }
        
        return TaskStatus::PENDING;
    }

    ExecutionResult PluginExecutor::GetTaskResult(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(results_mutex_);
        
        auto it = completed_results_.find(task_id);
        if (it != completed_results_.end()) {
            return it->second;
        }
        
        ExecutionResult result;
        result.status = TaskStatus::PENDING;
        return result;
    }

    std::vector<std::string> PluginExecutor::GetPendingTasks() {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        
        std::vector<std::string> pending;
        for (const auto& pair : tasks_) {
            if (pair.second->started_at == std::chrono::steady_clock::time_point{}) {
                pending.push_back(pair.first);
            }
        }
        
        return pending;
    }

    std::vector<std::string> PluginExecutor::GetRunningTasks() {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        
        std::vector<std::string> running;
        for (const auto& pair : tasks_) {
            if (pair.second->started_at != std::chrono::steady_clock::time_point{}) {
                running.push_back(pair.first);
            }
        }
        
        return running;
    }

    ExecutionResult PluginExecutor::ExecuteSync(const std::string& plugin_name,
                                               const std::string& function_name,
                                               const StringMap& parameters,
                                               std::chrono::milliseconds timeout) {
        
        auto future = ExecuteAsync(plugin_name, function_name, parameters, timeout);
        
        if (future.wait_for(timeout) == std::future_status::timeout) {
            ExecutionResult result;
            result.status = TaskStatus::TIMEOUT;
            result.error_message = "Task execution timeout";
            return result;
        }
        
        return future.get();
    }

    std::future<ExecutionResult> PluginExecutor::ExecuteAsync(const std::string& plugin_name,
                                                             const std::string& function_name,
                                                             const StringMap& parameters,
                                                             std::chrono::milliseconds timeout) {
        
        auto task = std::make_shared<PluginTask>();
        task->task_id = GenerateTaskId();
        task->plugin_name = plugin_name;
        task->function_name = function_name;
        task->parameters = parameters;
        task->timeout = timeout;
        
        auto future = task->promise.get_future();
        
        {
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            tasks_[task->task_id] = task;
        }
        
        thread_pool_->Submit([this, task]() {
            ExecuteTask(task);
        });
        
        return future;
    }

    std::vector<std::string> PluginExecutor::ExecuteBatch(const std::vector<PluginTask>& tasks) {
        std::vector<std::string> task_ids;
        
        for (const auto& task_template : tasks) {
            std::string task_id = SubmitTask(
                task_template.plugin_name,
                task_template.function_name,
                task_template.parameters,
                task_template.callback,
                task_template.priority,
                task_template.timeout
            );
            
            if (!task_id.empty()) {
                task_ids.push_back(task_id);
            }
        }
        
        LOG_INFO("Batch execution submitted: " + std::to_string(task_ids.size()) + " tasks");
        return task_ids;
    }

    bool PluginExecutor::IsPluginLoaded(const std::string& plugin_name) {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        return loaded_plugins_.find(plugin_name) != loaded_plugins_.end();
    }

    bool PluginExecutor::LoadPlugin(const std::string& plugin_name, const ByteArray& plugin_data) {
        // This is a placeholder implementation
        // In a real scenario, you would parse the plugin data and create an IPlugin instance
        LOG_INFO("Loading plugin: " + plugin_name);
        return true;
    }

    bool PluginExecutor::UnloadPlugin(const std::string& plugin_name) {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        
        auto it = loaded_plugins_.find(plugin_name);
        if (it == loaded_plugins_.end()) {
            return false;
        }
        
        // Cleanup plugin
        try {
            it->second->Cleanup();
            delete it->second;
        } catch (const std::exception& e) {
            LOG_ERROR("Error cleaning up plugin " + plugin_name + ": " + e.what());
        }
        
        loaded_plugins_.erase(it);
        LOG_INFO("Plugin unloaded: " + plugin_name);
        return true;
    }

    std::vector<std::string> PluginExecutor::GetLoadedPlugins() {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        
        std::vector<std::string> plugins;
        for (const auto& pair : loaded_plugins_) {
            plugins.push_back(pair.first);
        }
        
        return plugins;
    }

    void PluginExecutor::SetThreadPoolSize(size_t size) {
        // Recreate thread pool with new size
        thread_pool_->Shutdown();
        thread_pool_ = std::make_unique<ThreadPool>(size);
        LOG_INFO("Thread pool resized to " + std::to_string(size) + " threads");
    }

    size_t PluginExecutor::GetActiveTaskCount() const {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        return tasks_.size();
    }

    double PluginExecutor::GetAverageExecutionTime() const {
        if (completed_tasks_ == 0) {
            return 0.0;
        }
        
        return static_cast<double>(total_execution_time_.count()) / completed_tasks_;
    }

    void PluginExecutor::ClearCompletedTasks() {
        std::lock_guard<std::mutex> lock(results_mutex_);
        completed_results_.clear();
        LOG_INFO("Completed tasks cleared");
    }

    void PluginExecutor::Shutdown() {
        LOG_INFO("Shutting down plugin executor...");
        
        // Cancel all pending tasks
        {
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            for (auto& pair : tasks_) {
                CancelTask(pair.first);
            }
        }
        
        // Shutdown thread pool
        if (thread_pool_) {
            thread_pool_->Shutdown();
        }
        
        // Cleanup all plugins
        {
            std::lock_guard<std::mutex> lock(plugins_mutex_);
            for (auto& pair : loaded_plugins_) {
                try {
                    pair.second->Cleanup();
                    delete pair.second;
                } catch (const std::exception& e) {
                    LOG_ERROR("Error cleaning up plugin during shutdown: " + std::string(e.what()));
                }
            }
            loaded_plugins_.clear();
        }
        
        LOG_INFO("Plugin executor shutdown complete");
    }

    // Private methods

    void PluginExecutor::ExecuteTask(std::shared_ptr<PluginTask> task) {
        task->started_at = std::chrono::steady_clock::now();
        
        LOG_INFO("Executing task: " + task->task_id + " for plugin: " + task->plugin_name);
        
        ExecutionResult result;
        auto start_time = std::chrono::steady_clock::now();
        
        try {
            // Setup timeout handling
            SetupTaskTimeout(task);
            
            // Execute the plugin function
            result = CallPlugin(task->plugin_name, task->function_name, task->parameters);
            
            auto end_time = std::chrono::steady_clock::now();
            result.execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            if (result.status == TaskStatus::PENDING) {
                result.status = TaskStatus::COMPLETED;
            }
            
        } catch (const std::exception& e) {
            result.status = TaskStatus::FAILED;
            result.error_message = e.what();
            auto end_time = std::chrono::steady_clock::now();
            result.execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        }
        
        HandleTaskCompletion(task, result);
    }

    ExecutionResult PluginExecutor::CallPlugin(const std::string& plugin_name,
                                              const std::string& function_name,
                                              const StringMap& parameters) {
        
        ExecutionResult result;
        
        IPlugin* plugin = GetPlugin(plugin_name);
        if (!plugin) {
            result.status = TaskStatus::FAILED;
            result.error_message = "Plugin not found: " + plugin_name;
            return result;
        }
        
        if (!ValidatePlugin(plugin)) {
            result.status = TaskStatus::FAILED;
            result.error_message = "Plugin validation failed: " + plugin_name;
            return result;
        }
        
        try {
            // For simplicity, we'll call the Execute method
            // In a real implementation, you might use reflection or function pointers
            // to call specific functions based on function_name
            bool success = plugin->Execute(parameters, result.result_data);
            
            result.status = success ? TaskStatus::COMPLETED : TaskStatus::FAILED;
            if (!success && result.error_message.empty()) {
                result.error_message = "Plugin execution failed";
            }
            
        } catch (const std::exception& e) {
            result.status = TaskStatus::FAILED;
            result.error_message = "Plugin execution exception: " + std::string(e.what());
        }
        
        return result;
    }

    std::string PluginExecutor::GenerateTaskId() {
        static std::atomic<uint64_t> counter(0);
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);
        
        std::stringstream ss;
        ss << "task_" << std::hex << ++counter << "_";
        for (int i = 0; i < 8; ++i) {
            ss << std::hex << dis(gen);
        }
        
        return ss.str();
    }

    void PluginExecutor::CleanupTask(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        tasks_.erase(task_id);
    }

    bool PluginExecutor::IsTaskLimitReached() const {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        return tasks_.size() >= max_concurrent_tasks_;
    }

    IPlugin* PluginExecutor::GetPlugin(const std::string& plugin_name) {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        
        auto it = loaded_plugins_.find(plugin_name);
        if (it != loaded_plugins_.end()) {
            return it->second;
        }
        
        return nullptr;
    }

    bool PluginExecutor::ValidatePlugin(IPlugin* plugin) {
        if (!plugin) {
            return false;
        }
        
        try {
            // Basic validation - check if plugin can be initialized
            return true; // plugin->GetName() is accessible
        } catch (const std::exception&) {
            return false;
        }
    }

    void PluginExecutor::SetupTaskTimeout(std::shared_ptr<PluginTask> task) {
        // Setup a timer to handle task timeout
        thread_pool_->Submit([this, task]() {
            std::this_thread::sleep_for(task->timeout);
            
            // Check if task is still running
            {
                std::lock_guard<std::mutex> lock(tasks_mutex_);
                auto it = tasks_.find(task->task_id);
                if (it != tasks_.end()) {
                    HandleTaskTimeout(task->task_id);
                }
            }
        });
    }

    void PluginExecutor::HandleTaskTimeout(const std::string& task_id) {
        LOG_WARNING("Task timeout: " + task_id);
        
        ExecutionResult result;
        result.status = TaskStatus::TIMEOUT;
        result.error_message = "Task execution timeout";
        
        std::shared_ptr<PluginTask> task;
        {
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            auto it = tasks_.find(task_id);
            if (it != tasks_.end()) {
                task = it->second;
                tasks_.erase(it);
            }
        }
        
        if (task) {
            try {
                task->promise.set_value(result);
            } catch (const std::exception&) {
                // Promise may already be set
            }
            
            if (task->callback) {
                task->callback(false, "Task timeout");
            }
            
            HandleTaskCompletion(task, result);
        }
    }

    void PluginExecutor::HandleTaskError(std::shared_ptr<PluginTask> task, const std::string& error) {
        LOG_ERROR("Task error: " + task->task_id + " - " + error);
        
        ExecutionResult result;
        result.status = TaskStatus::FAILED;
        result.error_message = error;
        
        HandleTaskCompletion(task, result);
    }

    void PluginExecutor::HandleTaskCompletion(std::shared_ptr<PluginTask> task, const ExecutionResult& result) {
        // Store result
        {
            std::lock_guard<std::mutex> lock(results_mutex_);
            completed_results_[task->task_id] = result;
        }
        
        // Set promise
        try {
            task->promise.set_value(result);
        } catch (const std::exception&) {
            // Promise may already be set
        }
        
        // Call callback
        if (task->callback) {
            bool success = (result.status == TaskStatus::COMPLETED);
            task->callback(success, success ? result.result_data : result.error_message);
        }
        
        // Update statistics
        UpdateStatistics(result);
        
        // Cleanup task
        CleanupTask(task->task_id);
        
        LOG_INFO("Task completed: " + task->task_id + " - Status: " + std::to_string(static_cast<int>(result.status)));
    }

    void PluginExecutor::UpdateStatistics(const ExecutionResult& result) {
        if (result.status == TaskStatus::COMPLETED) {
            completed_tasks_++;
        } else {
            failed_tasks_++;
        }
        
        total_execution_time_ += result.execution_time;
    }

} // namespace Plugins