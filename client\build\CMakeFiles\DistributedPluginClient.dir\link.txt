"C:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\DistributedPluginClient.dir/objects.a
C:\Users\<USER>\Downloads\w64devkit\bin\ar.exe qc CMakeFiles\DistributedPluginClient.dir/objects.a @CMakeFiles\DistributedPluginClient.dir\objects1.rsp
C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe -Wall -Wextra -O3 -DNDEBUG -Wl,--subsystem,console -Wl,--gc-sections -Wl,--whole-archive CMakeFiles\DistributedPluginClient.dir/objects.a -Wl,--no-whole-archive -o DistributedPluginClient.exe -Wl,--out-implib,libDistributedPluginClient.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\DistributedPluginClient.dir\linkLibs.rsp
