#pragma once

#include "common.h"
#include "config.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#endif

#include <string>
#include <thread>
#include <atomic>
#include <functional>
#include <vector>
#include <mutex>

namespace Network {

class SimpleWebSocketClient {
public:
    enum class ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        RECONNECTING,
        FAILED
    };

    using MessageCallback = std::function<void(const std::string&)>;
    using StateCallback = std::function<void(ConnectionState)>;

    SimpleWebSocketClient();
    ~SimpleWebSocketClient();

    bool Connect(const std::string& host, uint16_t port, const std::string& path = "/");
    void Disconnect();
    bool Send(const std::string& message);
    bool SendBinary(const ByteArray& data);
    
    void SetMessageCallback(MessageCallback callback) { message_callback_ = callback; }
    void SetStateCallback(StateCallback callback) { state_callback_ = callback; }
    
    ConnectionState GetState() const { return state_; }
    bool IsConnected() const { return state_ == ConnectionState::CONNECTED; }
    
    void StartReconnectLoop();
    void StopReconnectLoop();
    
    void Run();
    void Stop();

    // Friend class for access to private members
    friend class WebSocketClient;

private:
    void OnOpen();
    void OnClose();
    void OnFail();
    void OnMessage(const std::string& message);
    
    void SetState(ConnectionState new_state);
    void ReconnectTask();
    void SendHeartbeat();
    void StartHeartbeat();
    void StopHeartbeat();
    void NetworkThread();
    
    bool PerformHandshake();
    bool SendRawData(const std::vector<uint8_t>& data);
    std::vector<uint8_t> ReceiveRawData();
    std::vector<uint8_t> CreateWebSocketFrame(const std::string& message, bool is_text = true);
    std::string ParseWebSocketFrame(const std::vector<uint8_t>& frame);
    
    std::string GenerateWebSocketKey();
    std::string GenerateAcceptKey(const std::string& key);
    
#ifdef _WIN32
    SOCKET socket_;
#else
    int socket_;
#endif
    
    std::thread network_thread_;
    std::thread reconnect_thread_;
    std::thread heartbeat_thread_;
    
    std::atomic<ConnectionState> state_;
    std::atomic<bool> should_reconnect_;
    std::atomic<bool> should_stop_;
    std::atomic<bool> heartbeat_enabled_;
    
    std::string server_host_;
    uint16_t server_port_;
    std::string server_path_;
    std::string server_uri_;
    
    MessageCallback message_callback_;
    StateCallback state_callback_;
    
    mutable std::mutex connection_mutex_;
    mutable std::mutex send_mutex_;
    
    uint32_t reconnect_attempts_;
    std::chrono::steady_clock::time_point last_heartbeat_;
    
    std::string GenerateClientId();
    std::string BuildUri(const std::string& host, uint16_t port, const std::string& path);
    
    // WebSocket protocol constants
    static constexpr uint8_t WS_FIN = 0x80;
    static constexpr uint8_t WS_OPCODE_TEXT = 0x01;
    static constexpr uint8_t WS_OPCODE_BINARY = 0x02;
    static constexpr uint8_t WS_OPCODE_CLOSE = 0x08;
    static constexpr uint8_t WS_OPCODE_PING = 0x09;
    static constexpr uint8_t WS_OPCODE_PONG = 0x0A;
    static constexpr uint8_t WS_MASK = 0x80;
};

} // namespace Network