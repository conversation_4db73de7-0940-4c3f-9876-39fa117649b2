# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\windows\client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\windows\client\build

# Include any dependencies generated for this target.
include CMakeFiles/DistributedPluginClient.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/DistributedPluginClient.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DistributedPluginClient.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DistributedPluginClient.dir/flags.make

CMakeFiles/DistributedPluginClient.dir/codegen:
.PHONY : CMakeFiles/DistributedPluginClient.dir/codegen

CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/main.cpp
CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\main.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\main.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\main.cpp

CMakeFiles/DistributedPluginClient.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/main.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\main.cpp > CMakeFiles\DistributedPluginClient.dir\src\main.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/main.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\main.cpp -o CMakeFiles\DistributedPluginClient.dir\src\main.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/crypto/xor/xor_crypto.cpp
CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\crypto\xor\xor_crypto.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\crypto\xor\xor_crypto.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\crypto\xor\xor_crypto.cpp

CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\crypto\xor\xor_crypto.cpp > CMakeFiles\DistributedPluginClient.dir\src\crypto\xor\xor_crypto.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\crypto\xor\xor_crypto.cpp -o CMakeFiles\DistributedPluginClient.dir\src\crypto\xor\xor_crypto.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/loader/pe_loader/pe_loader.cpp
CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\loader\pe_loader\pe_loader.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\loader\pe_loader\pe_loader.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\loader\pe_loader\pe_loader.cpp

CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\loader\pe_loader\pe_loader.cpp > CMakeFiles\DistributedPluginClient.dir\src\loader\pe_loader\pe_loader.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\loader\pe_loader\pe_loader.cpp -o CMakeFiles\DistributedPluginClient.dir\src\loader\pe_loader\pe_loader.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/obfuscator/string_obfuscator/string_obfuscator.cpp
CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\obfuscator\string_obfuscator\string_obfuscator.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\obfuscator\string_obfuscator\string_obfuscator.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\obfuscator\string_obfuscator\string_obfuscator.cpp

CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\obfuscator\string_obfuscator\string_obfuscator.cpp > CMakeFiles\DistributedPluginClient.dir\src\obfuscator\string_obfuscator\string_obfuscator.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\obfuscator\string_obfuscator\string_obfuscator.cpp -o CMakeFiles\DistributedPluginClient.dir\src\obfuscator\string_obfuscator\string_obfuscator.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/plugins/executor/plugin_executor.cpp
CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\plugins\executor\plugin_executor.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\plugins\executor\plugin_executor.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\plugins\executor\plugin_executor.cpp

CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\plugins\executor\plugin_executor.cpp > CMakeFiles\DistributedPluginClient.dir\src\plugins\executor\plugin_executor.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\plugins\executor\plugin_executor.cpp -o CMakeFiles\DistributedPluginClient.dir\src\plugins\executor\plugin_executor.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/network/protocol/protocol_handler.cpp
CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\network\protocol\protocol_handler.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\network\protocol\protocol_handler.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\network\protocol\protocol_handler.cpp

CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\network\protocol\protocol_handler.cpp > CMakeFiles\DistributedPluginClient.dir\src\network\protocol\protocol_handler.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\network\protocol\protocol_handler.cpp -o CMakeFiles\DistributedPluginClient.dir\src\network\protocol\protocol_handler.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/network/websocket/websocket_client.cpp
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\network\websocket\websocket_client.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\network\websocket\websocket_client.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\network\websocket\websocket_client.cpp

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\network\websocket\websocket_client.cpp > CMakeFiles\DistributedPluginClient.dir\src\network\websocket\websocket_client.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\network\websocket\websocket_client.cpp -o CMakeFiles\DistributedPluginClient.dir\src\network\websocket\websocket_client.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/common.cpp
CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\common.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\common.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\common.cpp

CMakeFiles/DistributedPluginClient.dir/src/common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/common.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\common.cpp > CMakeFiles\DistributedPluginClient.dir\src\common.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/common.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\common.cpp -o CMakeFiles\DistributedPluginClient.dir\src\common.cpp.s

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj: CMakeFiles/DistributedPluginClient.dir/flags.make
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj: CMakeFiles/DistributedPluginClient.dir/includes_CXX.rsp
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj: C:/Users/<USER>/Desktop/windows/client/src/network/websocket/simple_websocket_client_no_ssl.cpp
CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj: CMakeFiles/DistributedPluginClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj -MF CMakeFiles\DistributedPluginClient.dir\src\network\websocket\simple_websocket_client_no_ssl.cpp.obj.d -o CMakeFiles\DistributedPluginClient.dir\src\network\websocket\simple_websocket_client_no_ssl.cpp.obj -c C:\Users\<USER>\Desktop\windows\client\src\network\websocket\simple_websocket_client_no_ssl.cpp

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.i"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\windows\client\src\network\websocket\simple_websocket_client_no_ssl.cpp > CMakeFiles\DistributedPluginClient.dir\src\network\websocket\simple_websocket_client_no_ssl.cpp.i

CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.s"
	C:\Users\<USER>\Downloads\w64devkit\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\windows\client\src\network\websocket\simple_websocket_client_no_ssl.cpp -o CMakeFiles\DistributedPluginClient.dir\src\network\websocket\simple_websocket_client_no_ssl.cpp.s

# Object files for target DistributedPluginClient
DistributedPluginClient_OBJECTS = \
"CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj" \
"CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj"

# External object files for target DistributedPluginClient
DistributedPluginClient_EXTERNAL_OBJECTS =

DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/build.make
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/linkLibs.rsp
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/objects1.rsp
DistributedPluginClient.exe: CMakeFiles/DistributedPluginClient.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable DistributedPluginClient.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\DistributedPluginClient.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DistributedPluginClient.dir/build: DistributedPluginClient.exe
.PHONY : CMakeFiles/DistributedPluginClient.dir/build

CMakeFiles/DistributedPluginClient.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\DistributedPluginClient.dir\cmake_clean.cmake
.PHONY : CMakeFiles/DistributedPluginClient.dir/clean

CMakeFiles/DistributedPluginClient.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Desktop\windows\client C:\Users\<USER>\Desktop\windows\client C:\Users\<USER>\Desktop\windows\client\build C:\Users\<USER>\Desktop\windows\client\build C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles\DistributedPluginClient.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/DistributedPluginClient.dir/depend

