// Minimal nlohmann/json stub for compilation
#pragma once
#include <string>
#include <map>
#include <vector>
#include <sstream>
namespace nlohmann {
  class json {
  private:
    std::map<std::string, std::string> data_;
    
  public:
    json() = default;
    json(const std::string& s) {}
    json(const char* s) {}
    json(int i) {}
    json(bool b) {}
    json(long long ll) {}
    json(size_t sz) {}
    
    static json parse(const std::string& s) { return json{}; }
    static json array() { return json{}; }
    
    std::string dump() const { 
        if (data_.empty()) return "{}";
        std::stringstream ss;
        ss << "{";
        bool first = true;
        for (const auto& pair : data_) {
            if (!first) ss << ",";
            ss << "\"" << pair.first << "\":\"" << pair.second << "\"";
            first = false;
        }
        ss << "}";
        return ss.str();
    }
    bool empty() const { return true; }
    bool contains(const std::string& key) const { return false; }
    
    template<typename T> 
    T value(const std::string& key, const T& default_val) const { return default_val; }
    
    std::string value(const std::string& key, const char* default_val) const { return std::string(default_val); }
    
    json& operator[](const std::string& key) { 
        // This is a simplified version - we'll handle assignment in operator=
        return *this; 
    }
    const json& operator[](const std::string& key) const { return *this; }
    json& operator[](const char* key) { return *this; }
    const json& operator[](const char* key) const { return *this; }
    
    operator std::string() const { return ""; }
    operator int() const { return 0; }
    operator size_t() const { return 0; }
    operator bool() const { return false; }
    operator long long() const { return 0; }
    
    json& operator=(const std::string& s) { return *this; }
    json& operator=(const char* s) { return *this; }
    json& operator=(int i) { return *this; }
    json& operator=(bool b) { return *this; }
    json& operator=(long long ll) { return *this; }
    json& operator=(size_t sz) { return *this; }
    json& operator=(unsigned int ui) { return *this; }
    json& operator=(unsigned long ul) { return *this; }
    
    void push_back(const json& j) {}
    
    class iterator {
    public:
      std::pair<std::string, json> operator*() { return {"", json{}}; }
      iterator& operator++() { return *this; }
      bool operator!=(const iterator& other) { return false; }
    };
    iterator begin() { return iterator{}; }
    iterator end() { return iterator{}; }
    std::vector<std::pair<std::string, json>> items() { return std::vector<std::pair<std::string, json>>{}; }
  };
} 
