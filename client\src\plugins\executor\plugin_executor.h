#pragma once

#include "common.h"
#include "config.h"
#include <thread>
#include <queue>
#include <future>

namespace Plugins {

    enum class TaskStatus {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED,
        CANCELLED,
        TIMEOUT
    };

    struct ExecutionResult {
        TaskStatus status;
        std::string result_data;
        std::string error_message;
        std::chrono::milliseconds execution_time;
        int exit_code;
        
        ExecutionResult() 
            : status(TaskStatus::PENDING)
            , execution_time(0)
            , exit_code(0) {}
    };

    struct PluginTask {
        std::string task_id;
        std::string plugin_name;
        std::string function_name;
        StringMap parameters;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point started_at;
        std::chrono::milliseconds timeout;
        uint32_t priority;
        uint32_t retry_count;
        uint32_t max_retries;
        TaskCallback callback;
        std::promise<ExecutionResult> promise;
        
        PluginTask() 
            : created_at(std::chrono::steady_clock::now())
            , timeout(std::chrono::minutes(5))
            , priority(0)
            , retry_count(0)
            , max_retries(3) {}
    };

    class ThreadPool {
    public:
        explicit ThreadPool(size_t thread_count = Config::THREAD_POOL_SIZE);
        ~ThreadPool();

        template<typename F, typename... Args>
        std::future<void> Submit(F&& f, Args&&... args);
        
        void Shutdown();
        size_t GetActiveThreads() const { return active_threads_; }
        size_t GetQueueSize() const;

    private:
        std::vector<std::thread> workers_;
        std::queue<std::function<void()>> tasks_;
        std::mutex queue_mutex_;
        std::condition_variable condition_;
        std::atomic<bool> stop_;
        std::atomic<size_t> active_threads_;
    };

    class PluginExecutor {
    public:
        PluginExecutor();
        ~PluginExecutor();

        // Task management
        std::string SubmitTask(const std::string& plugin_name,
                              const std::string& function_name,
                              const StringMap& parameters,
                              TaskCallback callback = nullptr,
                              uint32_t priority = 0,
                              std::chrono::milliseconds timeout = std::chrono::minutes(5));
        
        bool CancelTask(const std::string& task_id);
        TaskStatus GetTaskStatus(const std::string& task_id);
        ExecutionResult GetTaskResult(const std::string& task_id);
        std::vector<std::string> GetPendingTasks();
        std::vector<std::string> GetRunningTasks();
        
        // Synchronous execution
        ExecutionResult ExecuteSync(const std::string& plugin_name,
                                   const std::string& function_name,
                                   const StringMap& parameters,
                                   std::chrono::milliseconds timeout = std::chrono::minutes(5));
        
        // Asynchronous execution
        std::future<ExecutionResult> ExecuteAsync(const std::string& plugin_name,
                                                 const std::string& function_name,
                                                 const StringMap& parameters,
                                                 std::chrono::milliseconds timeout = std::chrono::minutes(5));
        
        // Batch execution
        std::vector<std::string> ExecuteBatch(const std::vector<PluginTask>& tasks);
        
        // Plugin management
        bool IsPluginLoaded(const std::string& plugin_name);
        bool LoadPlugin(const std::string& plugin_name, const ByteArray& plugin_data);
        bool UnloadPlugin(const std::string& plugin_name);
        std::vector<std::string> GetLoadedPlugins();
        
        // Configuration
        void SetMaxConcurrentTasks(size_t max_tasks) { max_concurrent_tasks_ = max_tasks; }
        void SetDefaultTimeout(std::chrono::milliseconds timeout) { default_timeout_ = timeout; }
        void SetThreadPoolSize(size_t size);
        
        // Statistics
        size_t GetCompletedTaskCount() const { return completed_tasks_; }
        size_t GetFailedTaskCount() const { return failed_tasks_; }
        size_t GetActiveTaskCount() const;
        double GetAverageExecutionTime() const;
        
        // Cleanup
        void ClearCompletedTasks();
        void Shutdown();

    private:
        std::unique_ptr<ThreadPool> thread_pool_;
        std::map<std::string, std::shared_ptr<PluginTask>> tasks_;
        std::map<std::string, ExecutionResult> completed_results_;
        std::map<std::string, IPlugin*> loaded_plugins_;
        
        mutable std::mutex tasks_mutex_;
        mutable std::mutex results_mutex_;
        mutable std::mutex plugins_mutex_;
        
        std::atomic<size_t> max_concurrent_tasks_;
        std::atomic<size_t> completed_tasks_;
        std::atomic<size_t> failed_tasks_;
        std::chrono::milliseconds default_timeout_;
        
        // Task execution
        void ExecuteTask(std::shared_ptr<PluginTask> task);
        ExecutionResult CallPlugin(const std::string& plugin_name,
                                  const std::string& function_name,
                                  const StringMap& parameters);
        
        // Task management helpers
        std::string GenerateTaskId();
        void CleanupTask(const std::string& task_id);
        bool IsTaskLimitReached() const;
        
        // Plugin helpers
        IPlugin* GetPlugin(const std::string& plugin_name);
        bool ValidatePlugin(IPlugin* plugin);
        
        // Timeout handling
        void SetupTaskTimeout(std::shared_ptr<PluginTask> task);
        void HandleTaskTimeout(const std::string& task_id);
        
        // Error handling
        void HandleTaskError(std::shared_ptr<PluginTask> task, const std::string& error);
        void HandleTaskCompletion(std::shared_ptr<PluginTask> task, const ExecutionResult& result);
        
        // Statistics helpers
        void UpdateStatistics(const ExecutionResult& result);
        std::chrono::milliseconds total_execution_time_;
    };

    // Template implementation for ThreadPool::Submit
    template<typename F, typename... Args>
    std::future<void> ThreadPool::Submit(F&& f, Args&&... args) {
        auto task = std::make_shared<std::packaged_task<void()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<void> result = task->get_future();
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            if (stop_) {
                throw std::runtime_error("Cannot submit to stopped ThreadPool");
            }
            
            tasks_.emplace([task](){ (*task)(); });
        }
        
        condition_.notify_one();
        return result;
    }

} // namespace Plugins