#pragma once

#include "common.h"
#include "config.h"
#include "crypto/xor/xor_crypto.h"
#include <nlohmann/json.hpp>

namespace Network {

    enum class MessageType {
        UNKNOWN = 0,
        
        // Connection management
        CONNECT = 1,
        DISCONNECT = 2,
        HEARTBEAT = 3,
        HEARTBEAT_RESPONSE = 4,
        
        // Client information
        CLIENT_INFO = 10,
        CLIENT_INFO_RESPONSE = 11,
        SYSTEM_INFO = 12,
        SYSTEM_INFO_RESPONSE = 13,
        
        // Plugin management
        PLUGIN_LIST = 20,
        PLUGIN_LIST_RESPONSE = 21,
        PLUGIN_LOAD = 22,
        PLUGIN_LOAD_RESPONSE = 23,
        PLUGIN_UNLOAD = 24,
        PLUGIN_UNLOAD_RESPONSE = 25,
        PLUGIN_EXECUTE = 26,
        PLUGIN_EXECUTE_RESPONSE = 27,
        
        // File transfer
        FILE_TRANSFER_START = 30,
        FILE_TRANSFER_CHUNK = 31,
        FILE_TRANSFER_END = 32,
        FILE_TRANSFER_STATUS = 33,
        
        // Task management
        TASK_CREATE = 40,
        TASK_STATUS = 41,
        TASK_RESULT = 42,
        TASK_CANCEL = 43,
        
        // Error handling
        ERROR = 50,
        
        // Custom messages
        CUSTOM = 100
    };

    struct ProtocolMessage {
        MessageType type;
        std::string id;
        std::string client_id;
        std::string target;
        nlohmann::json payload;
        StringMap metadata;
        bool encrypted;
        uint64_t timestamp;
        uint32_t sequence_number;
        
        ProtocolMessage() 
            : type(MessageType::UNKNOWN)
            , encrypted(true)
            , timestamp(0)
            , sequence_number(0) {}
    };

    struct FileTransferInfo {
        std::string transfer_id;
        std::string filename;
        size_t total_size;
        size_t chunk_size;
        size_t chunks_total;
        size_t chunks_received;
        ByteArray data;
        std::chrono::steady_clock::time_point start_time;
        bool completed;
        bool failed;
        std::string error_message;
        
        FileTransferInfo() 
            : total_size(0)
            , chunk_size(Config::CHUNK_SIZE)
            , chunks_total(0)
            , chunks_received(0)
            , completed(false)
            , failed(false) {}
    };

    class ProtocolHandler {
    public:
        using MessageCallback = std::function<void(const ProtocolMessage&)>;
        using ErrorCallback = std::function<void(const std::string&)>;

        ProtocolHandler();
        ~ProtocolHandler() = default;

        // Message handling
        bool SendMessage(const ProtocolMessage& message);
        bool ParseMessage(const std::string& raw_message, ProtocolMessage& message);
        std::string SerializeMessage(const ProtocolMessage& message);
        
        // Message creation helpers
        ProtocolMessage CreateHeartbeat();
        ProtocolMessage CreateClientInfo();
        ProtocolMessage CreateSystemInfo();
        ProtocolMessage CreatePluginLoadResponse(const std::string& plugin_name, bool success, const std::string& error = "");
        ProtocolMessage CreatePluginExecuteResponse(const std::string& task_id, bool success, const std::string& result, const std::string& error = "");
        ProtocolMessage CreateError(const std::string& error_message, const std::string& error_code = "");
        
        // File transfer
        bool StartFileTransfer(const std::string& transfer_id, const std::string& filename, const ByteArray& data);
        bool HandleFileTransferChunk(const ProtocolMessage& message);
        bool CompleteFileTransfer(const std::string& transfer_id, ByteArray& data);
        std::vector<std::string> GetActiveTransfers();
        
        // Callbacks
        void SetMessageCallback(MessageCallback callback) { message_callback_ = callback; }
        void SetErrorCallback(ErrorCallback callback) { error_callback_ = callback; }
        void SetSendFunction(std::function<bool(const std::string&)> send_func) { send_function_ = send_func; }
        
        // Configuration
        void SetEncryptionEnabled(bool enabled) { encryption_enabled_ = enabled; }
        void SetCompressionEnabled(bool enabled) { compression_enabled_ = enabled; }
        void SetClientId(const std::string& client_id) { client_id_ = client_id; }
        
        // Statistics
        uint64_t GetMessagesSent() const { return messages_sent_; }
        uint64_t GetMessagesReceived() const { return messages_received_; }
        uint64_t GetBytesTransferred() const { return bytes_transferred_; }
        
        // Validation
        bool ValidateMessage(const ProtocolMessage& message);
        bool ValidatePayload(MessageType type, const nlohmann::json& payload);
        
    private:
        std::unique_ptr<Crypto::XORCrypto> crypto_;
        MessageCallback message_callback_;
        ErrorCallback error_callback_;
        std::function<bool(const std::string&)> send_function_;
        
        std::map<std::string, FileTransferInfo> active_transfers_;
        mutable std::mutex transfers_mutex_;
        
        std::string client_id_;
        std::atomic<uint32_t> sequence_counter_;
        std::atomic<bool> encryption_enabled_;
        std::atomic<bool> compression_enabled_;
        
        // Statistics
        std::atomic<uint64_t> messages_sent_;
        std::atomic<uint64_t> messages_received_;
        std::atomic<uint64_t> bytes_transferred_;
        
        // Message processing
        bool ProcessMessage(const ProtocolMessage& message);
        bool HandleConnectionMessage(const ProtocolMessage& message);
        bool HandlePluginMessage(const ProtocolMessage& message);
        bool HandleFileTransferMessage(const ProtocolMessage& message);
        bool HandleTaskMessage(const ProtocolMessage& message);
        
        // Encryption/Decryption
        std::string EncryptPayload(const std::string& payload);
        std::string DecryptPayload(const std::string& encrypted_payload);
        
        // Compression
        std::string CompressData(const std::string& data);
        std::string DecompressData(const std::string& compressed_data);
        
        // Utility functions
        std::string GenerateMessageId();
        uint64_t GetCurrentTimestamp();
        MessageType StringToMessageType(const std::string& type_str);
        std::string MessageTypeToString(MessageType type);
        
        // Validation helpers
        bool ValidateClientInfo(const nlohmann::json& payload);
        bool ValidatePluginLoad(const nlohmann::json& payload);
        bool ValidatePluginExecute(const nlohmann::json& payload);
        bool ValidateFileTransfer(const nlohmann::json& payload);
        
        // Error handling
        void HandleError(const std::string& error_message);
        void SendErrorResponse(const std::string& original_message_id, const std::string& error_message);
        
        // File transfer helpers
        std::vector<ByteArray> SplitIntoChunks(const ByteArray& data, size_t chunk_size);
        bool SendFileChunk(const std::string& transfer_id, size_t chunk_index, const ByteArray& chunk);
    };

    // Protocol constants
    namespace Protocol {
        const std::string VERSION = "1.0";
        const std::string MAGIC_HEADER = "DPMS"; // Distributed Plugin Management System
        const uint32_t MAX_MESSAGE_SIZE = 10 * 1024 * 1024; // 10MB
        const uint32_t MAX_PAYLOAD_SIZE = 8 * 1024 * 1024;  // 8MB
        const uint32_t CHUNK_SIZE = 64 * 1024; // 64KB
        const uint32_t MAX_TRANSFER_TIMEOUT_MS = 300000; // 5 minutes
        
        // Message field names
        const std::string FIELD_TYPE = "type";
        const std::string FIELD_ID = "id";
        const std::string FIELD_CLIENT_ID = "client_id";
        const std::string FIELD_TARGET = "target";
        const std::string FIELD_PAYLOAD = "payload";
        const std::string FIELD_METADATA = "metadata";
        const std::string FIELD_ENCRYPTED = "encrypted";
        const std::string FIELD_TIMESTAMP = "timestamp";
        const std::string FIELD_SEQUENCE = "sequence";
        const std::string FIELD_VERSION = "version";
        
        // Standard metadata fields
        const std::string META_CLIENT_TYPE = "client_type";
        const std::string META_PROTOCOL_VERSION = "protocol_version";
        const std::string META_COMPRESSION = "compression";
        const std::string META_CHECKSUM = "checksum";
    }

    // Message validation schemas
    namespace Schema {
        bool ValidateHeartbeat(const nlohmann::json& payload);
        bool ValidateClientInfo(const nlohmann::json& payload);
        bool ValidateSystemInfo(const nlohmann::json& payload);
        bool ValidatePluginLoad(const nlohmann::json& payload);
        bool ValidatePluginExecute(const nlohmann::json& payload);
        bool ValidateFileTransfer(const nlohmann::json& payload);
        bool ValidateTaskCreate(const nlohmann::json& payload);
    }

} // namespace Network