const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');
const { ChunkProtocol } = require('../protocols/chunk-protocol');

// 传输方向
const TRANSFER_DIRECTION = {
    UPLOAD: 'upload',     // 客户端 -> 服务器
    DOWNLOAD: 'download'  // 服务器 -> 客户端
};

// 文件传输状态
const FILE_TRANSFER_STATUS = {
    PENDING: 'pending',
    INITIALIZING: 'initializing',
    TRANSFERRING: 'transferring',
    VERIFYING: 'verifying',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};

class FileTransferManager extends EventEmitter {
    constructor(options = {}) {
        super();
        this.uploadsDir = options.uploadsDir || path.join(__dirname, '../../uploads');
        this.downloadsDir = options.downloadsDir || path.join(__dirname, '../../downloads');
        this.tempDir = options.tempDir || path.join(__dirname, '../../temp');
        this.maxFileSize = options.maxFileSize || 1024 * 1024 * 1024; // 1GB
        this.allowedExtensions = options.allowedExtensions || null; // null = 允许所有
        this.quarantineEnabled = options.quarantineEnabled || true;
        
        // 分块传输协议
        this.chunkProtocol = new ChunkProtocol(options.chunkOptions);
        
        // 文件传输会话
        this.transfers = new Map();
        
        // 客户端传输映射
        this.clientTransfers = new Map();
        
        // 统计信息
        this.stats = {
            totalTransfers: 0,
            activeTransfers: 0,
            completedTransfers: 0,
            failedTransfers: 0,
            totalBytesTransferred: 0,
            totalUploadBytes: 0,
            totalDownloadBytes: 0
        };
        
        this.setupChunkProtocolEvents();
    }

    // 初始化文件传输管理器
    async initialize() {
        try {
            // 确保目录存在
            await this.ensureDirectories();
            
            console.log('文件传输管理器初始化完成');
            this.emit('initialized');
            
        } catch (error) {
            console.error('文件传输管理器初始化失败:', error);
            throw error;
        }
    }

    // 确保必要目录存在
    async ensureDirectories() {
        const dirs = [this.uploadsDir, this.downloadsDir, this.tempDir];
        
        for (const dir of dirs) {
            try {
                await fs.access(dir);
            } catch (error) {
                console.log(`创建目录: ${dir}`);
                await fs.mkdir(dir, { recursive: true });
            }
        }
    }

    // 设置分块协议事件监听
    setupChunkProtocolEvents() {
        this.chunkProtocol.on('transferInitialized', (transfer) => {
            const fileTransfer = this.findTransferByChunkId(transfer.id);
            if (fileTransfer) {
                this.updateTransferStatus(fileTransfer.id, FILE_TRANSFER_STATUS.TRANSFERRING);
            }
        });

        this.chunkProtocol.on('transferCompleted', (transfer, reconstructedData) => {
            const fileTransfer = this.findTransferByChunkId(transfer.id);
            if (fileTransfer) {
                this.handleChunkTransferCompleted(fileTransfer, reconstructedData);
            }
        });

        this.chunkProtocol.on('transferFailed', (transfer, reason) => {
            const fileTransfer = this.findTransferByChunkId(transfer.id);
            if (fileTransfer) {
                this.failFileTransfer(fileTransfer.id, `分块传输失败: ${reason}`);
            }
        });

        this.chunkProtocol.on('chunkSent', (chunk, message) => {
            // 转发分块消息到相应的客户端
            const fileTransfer = this.findTransferByChunkId(chunk.transferId);
            if (fileTransfer) {
                this.emit('chunkMessage', fileTransfer.clientId, message);
            }
        });
    }

    // 初始化文件上传
    async initializeUpload(clientId, fileInfo) {
        try {
            this.validateFileInfo(fileInfo);
            
            const transferId = uuidv4();
            const transfer = {
                id: transferId,
                direction: TRANSFER_DIRECTION.UPLOAD,
                clientId: clientId,
                fileName: fileInfo.fileName,
                originalName: fileInfo.originalName || fileInfo.fileName,
                fileSize: fileInfo.fileSize,
                mimeType: fileInfo.mimeType || 'application/octet-stream',
                checksum: fileInfo.checksum,
                status: FILE_TRANSFER_STATUS.PENDING,
                createdAt: Date.now(),
                startedAt: null,
                completedAt: null,
                tempFilePath: null,
                finalFilePath: null,
                chunkTransferId: null,
                progress: 0,
                error: null,
                metadata: fileInfo.metadata || {}
            };

            this.transfers.set(transferId, transfer);
            this.addClientTransfer(clientId, transferId);
            this.stats.totalTransfers++;
            this.stats.activeTransfers++;

            console.log(`初始化文件上传: ${transfer.originalName} (${transfer.fileSize} bytes) from ${clientId}`);
            this.emit('uploadInitialized', transfer);

            return transferId;
            
        } catch (error) {
            console.error('初始化文件上传失败:', error);
            throw error;
        }
    }

    // 开始文件上传
    async startUpload(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            throw new Error(`传输会话不存在: ${transferId}`);
        }

        if (transfer.status !== FILE_TRANSFER_STATUS.PENDING) {
            throw new Error(`传输状态无效: ${transfer.status}`);
        }

        try {
            transfer.status = FILE_TRANSFER_STATUS.INITIALIZING;
            transfer.startedAt = Date.now();

            // 生成临时文件路径
            const tempFileName = `${transferId}_${transfer.fileName}`;
            transfer.tempFilePath = path.join(this.tempDir, tempFileName);

            console.log(`开始文件上传: ${transfer.originalName}`);
            this.emit('uploadStarted', transfer);

            // 通知客户端开始上传
            this.emit('requestFileData', transfer.clientId, {
                type: 'file_upload_request',
                transferId: transferId,
                fileName: transfer.fileName,
                fileSize: transfer.fileSize
            });

            return transfer;
            
        } catch (error) {
            this.failFileTransfer(transferId, `开始上传失败: ${error.message}`);
            throw error;
        }
    }

    // 接收上传的文件数据
    async receiveUploadData(transferId, fileData) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            throw new Error(`传输会话不存在: ${transferId}`);
        }

        if (transfer.direction !== TRANSFER_DIRECTION.UPLOAD) {
            throw new Error('无效的传输方向');
        }

        try {
            // 使用分块协议处理文件数据
            const chunkTransferId = `file_${transferId}`;
            transfer.chunkTransferId = chunkTransferId;

            this.chunkProtocol.initializeTransfer(chunkTransferId, fileData, {
                clientId: transfer.clientId,
                transferType: 'file_upload',
                metadata: {
                    originalTransferId: transferId,
                    fileName: transfer.fileName,
                    fileSize: transfer.fileSize
                }
            });

            this.chunkProtocol.startTransfer(chunkTransferId);

        } catch (error) {
            this.failFileTransfer(transferId, `接收上传数据失败: ${error.message}`);
            throw error;
        }
    }

    // 处理分块传输完成
    async handleChunkTransferCompleted(fileTransfer, reconstructedData) {
        try {
            // 验证文件大小
            if (reconstructedData.length !== fileTransfer.fileSize) {
                throw new Error(`文件大小不匹配: 期望 ${fileTransfer.fileSize}, 实际 ${reconstructedData.length}`);
            }

            // 验证校验和（如果提供）
            if (fileTransfer.checksum) {
                const calculatedChecksum = crypto.createHash('sha256').update(reconstructedData).digest('hex');
                if (calculatedChecksum !== fileTransfer.checksum) {
                    throw new Error('文件校验和不匹配');
                }
            }

            fileTransfer.status = FILE_TRANSFER_STATUS.VERIFYING;

            // 保存文件到临时位置
            await fs.writeFile(fileTransfer.tempFilePath, reconstructedData);

            // 可选：病毒扫描或其他安全检查
            if (this.quarantineEnabled) {
                await this.performSecurityScan(fileTransfer);
            }

            // 移动到最终位置
            await this.finalizeUpload(fileTransfer);

        } catch (error) {
            this.failFileTransfer(fileTransfer.id, `处理传输完成失败: ${error.message}`);
        }
    }

    // 完成文件上传
    async finalizeUpload(transfer) {
        try {
            // 生成最终文件路径
            const finalFileName = `${Date.now()}_${transfer.fileName}`;
            transfer.finalFilePath = path.join(this.uploadsDir, finalFileName);

            // 移动文件到最终位置
            await fs.rename(transfer.tempFilePath, transfer.finalFilePath);

            // 更新传输状态
            this.completeFileTransfer(transfer.id);

            console.log(`文件上传完成: ${transfer.originalName} -> ${transfer.finalFilePath}`);

        } catch (error) {
            this.failFileTransfer(transfer.id, `完成上传失败: ${error.message}`);
            throw error;
        }
    }

    // 初始化文件下载
    async initializeDownload(clientId, filePath, fileName = null) {
        try {
            // 验证文件存在
            const stats = await fs.stat(filePath);
            if (!stats.isFile()) {
                throw new Error('指定路径不是文件');
            }

            const transferId = uuidv4();
            const actualFileName = fileName || path.basename(filePath);
            
            const transfer = {
                id: transferId,
                direction: TRANSFER_DIRECTION.DOWNLOAD,
                clientId: clientId,
                fileName: actualFileName,
                originalName: actualFileName,
                fileSize: stats.size,
                mimeType: this.getMimeType(filePath),
                checksum: null, // 将在读取文件时计算
                status: FILE_TRANSFER_STATUS.PENDING,
                createdAt: Date.now(),
                startedAt: null,
                completedAt: null,
                sourceFilePath: filePath,
                chunkTransferId: null,
                progress: 0,
                error: null,
                metadata: {}
            };

            this.transfers.set(transferId, transfer);
            this.addClientTransfer(clientId, transferId);
            this.stats.totalTransfers++;
            this.stats.activeTransfers++;

            console.log(`初始化文件下载: ${actualFileName} (${stats.size} bytes) to ${clientId}`);
            this.emit('downloadInitialized', transfer);

            return transferId;
            
        } catch (error) {
            console.error('初始化文件下载失败:', error);
            throw error;
        }
    }

    // 开始文件下载
    async startDownload(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            throw new Error(`传输会话不存在: ${transferId}`);
        }

        if (transfer.status !== FILE_TRANSFER_STATUS.PENDING) {
            throw new Error(`传输状态无效: ${transfer.status}`);
        }

        try {
            transfer.status = FILE_TRANSFER_STATUS.INITIALIZING;
            transfer.startedAt = Date.now();

            // 读取文件数据
            const fileData = await fs.readFile(transfer.sourceFilePath);
            
            // 计算校验和
            transfer.checksum = crypto.createHash('sha256').update(fileData).digest('hex');

            // 使用分块协议发送文件
            const chunkTransferId = `file_${transferId}`;
            transfer.chunkTransferId = chunkTransferId;

            this.chunkProtocol.initializeTransfer(chunkTransferId, fileData, {
                clientId: transfer.clientId,
                transferType: 'file_download',
                metadata: {
                    originalTransferId: transferId,
                    fileName: transfer.fileName,
                    fileSize: transfer.fileSize,
                    checksum: transfer.checksum
                }
            });

            this.chunkProtocol.startTransfer(chunkTransferId);

            console.log(`开始文件下载: ${transfer.originalName}`);
            this.emit('downloadStarted', transfer);

            return transfer;
            
        } catch (error) {
            this.failFileTransfer(transferId, `开始下载失败: ${error.message}`);
            throw error;
        }
    }

    // 处理分块确认
    handleChunkAck(ackMessage) {
        this.chunkProtocol.handleChunkAck(ackMessage);
    }

    // 验证文件信息
    validateFileInfo(fileInfo) {
        const required = ['fileName', 'fileSize'];
        
        for (const field of required) {
            if (!fileInfo[field]) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }

        // 检查文件大小
        if (fileInfo.fileSize > this.maxFileSize) {
            throw new Error(`文件过大: ${fileInfo.fileSize} > ${this.maxFileSize}`);
        }

        // 检查文件扩展名
        if (this.allowedExtensions) {
            const ext = path.extname(fileInfo.fileName).toLowerCase();
            if (!this.allowedExtensions.includes(ext)) {
                throw new Error(`不允许的文件类型: ${ext}`);
            }
        }

        // 检查文件名安全性
        if (fileInfo.fileName.includes('..') || fileInfo.fileName.includes('/') || fileInfo.fileName.includes('\\')) {
            throw new Error('文件名包含非法字符');
        }
    }

    // 执行安全扫描
    async performSecurityScan(transfer) {
        // 这里可以集成病毒扫描或其他安全检查
        // 暂时只做基本的文件类型检查
        
        try {
            const stats = await fs.stat(transfer.tempFilePath);
            
            // 检查文件是否为空
            if (stats.size === 0) {
                throw new Error('文件为空');
            }

            // 检查文件头（魔术字节）
            const buffer = Buffer.alloc(16);
            const fd = await fs.open(transfer.tempFilePath, 'r');
            await fd.read(buffer, 0, 16, 0);
            await fd.close();

            // 简单的文件类型验证
            this.validateFileHeader(buffer, transfer.fileName);

            console.log(`安全扫描通过: ${transfer.originalName}`);
            
        } catch (error) {
            console.error(`安全扫描失败: ${transfer.originalName}`, error);
            throw error;
        }
    }

    // 验证文件头
    validateFileHeader(buffer, fileName) {
        const ext = path.extname(fileName).toLowerCase();
        
        // 常见文件类型的魔术字节
        const signatures = {
            '.exe': [0x4D, 0x5A], // MZ
            '.dll': [0x4D, 0x5A], // MZ
            '.pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
            '.zip': [0x50, 0x4B, 0x03, 0x04], // PK..
            '.png': [0x89, 0x50, 0x4E, 0x47], // .PNG
            '.jpg': [0xFF, 0xD8, 0xFF],
            '.gif': [0x47, 0x49, 0x46, 0x38] // GIF8
        };

        if (signatures[ext]) {
            const signature = signatures[ext];
            for (let i = 0; i < signature.length; i++) {
                if (buffer[i] !== signature[i]) {
                    throw new Error(`文件头不匹配，可能不是真正的 ${ext} 文件`);
                }
            }
        }
    }

    // 获取MIME类型
    getMimeType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const mimeTypes = {
            '.txt': 'text/plain',
            '.pdf': 'application/pdf',
            '.zip': 'application/zip',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.exe': 'application/x-msdownload',
            '.dll': 'application/x-msdownload'
        };
        
        return mimeTypes[ext] || 'application/octet-stream';
    }

    // 更新传输状态
    updateTransferStatus(transferId, status) {
        const transfer = this.transfers.get(transferId);
        if (transfer) {
            const oldStatus = transfer.status;
            transfer.status = status;
            
            console.log(`传输状态更新: ${transfer.originalName} ${oldStatus} -> ${status}`);
            this.emit('transferStatusChanged', transfer, oldStatus, status);
        }
    }

    // 完成文件传输
    completeFileTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return false;
        }

        transfer.status = FILE_TRANSFER_STATUS.COMPLETED;
        transfer.completedAt = Date.now();
        transfer.progress = 100;

        this.stats.activeTransfers--;
        this.stats.completedTransfers++;
        this.stats.totalBytesTransferred += transfer.fileSize;

        if (transfer.direction === TRANSFER_DIRECTION.UPLOAD) {
            this.stats.totalUploadBytes += transfer.fileSize;
        } else {
            this.stats.totalDownloadBytes += transfer.fileSize;
        }

        const duration = transfer.completedAt - transfer.startedAt;
        const speed = (transfer.fileSize / duration * 1000).toFixed(2); // bytes/sec

        console.log(`文件传输完成: ${transfer.originalName}, 耗时: ${duration}ms, 速度: ${speed} B/s`);
        this.emit('transferCompleted', transfer);

        return true;
    }

    // 传输失败
    failFileTransfer(transferId, reason) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return false;
        }

        transfer.status = FILE_TRANSFER_STATUS.FAILED;
        transfer.completedAt = Date.now();
        transfer.error = reason;

        this.stats.activeTransfers--;
        this.stats.failedTransfers++;

        // 清理临时文件
        if (transfer.tempFilePath) {
            fs.unlink(transfer.tempFilePath).catch(error => {
                console.warn(`清理临时文件失败: ${transfer.tempFilePath}`, error);
            });
        }

        console.error(`文件传输失败: ${transfer.originalName}, 原因: ${reason}`);
        this.emit('transferFailed', transfer, reason);

        return true;
    }

    // 取消文件传输
    cancelTransfer(transferId, reason = '手动取消') {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return false;
        }

        transfer.status = FILE_TRANSFER_STATUS.CANCELLED;
        transfer.completedAt = Date.now();
        transfer.error = reason;

        this.stats.activeTransfers--;

        // 取消分块传输
        if (transfer.chunkTransferId) {
            this.chunkProtocol.cancelTransfer(transfer.chunkTransferId);
        }

        // 清理临时文件
        if (transfer.tempFilePath) {
            fs.unlink(transfer.tempFilePath).catch(error => {
                console.warn(`清理临时文件失败: ${transfer.tempFilePath}`, error);
            });
        }

        console.log(`取消文件传输: ${transfer.originalName}, 原因: ${reason}`);
        this.emit('transferCancelled', transfer, reason);

        return true;
    }

    // 根据分块传输ID查找文件传输
    findTransferByChunkId(chunkTransferId) {
        for (const transfer of this.transfers.values()) {
            if (transfer.chunkTransferId === chunkTransferId) {
                return transfer;
            }
        }
        return null;
    }

    // 添加客户端传输映射
    addClientTransfer(clientId, transferId) {
        if (!this.clientTransfers.has(clientId)) {
            this.clientTransfers.set(clientId, new Set());
        }
        this.clientTransfers.get(clientId).add(transferId);
    }

    // 获取传输信息
    getTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return null;
        }

        return {
            id: transfer.id,
            direction: transfer.direction,
            clientId: transfer.clientId,
            fileName: transfer.fileName,
            originalName: transfer.originalName,
            fileSize: transfer.fileSize,
            mimeType: transfer.mimeType,
            status: transfer.status,
            progress: transfer.progress,
            createdAt: transfer.createdAt,
            startedAt: transfer.startedAt,
            completedAt: transfer.completedAt,
            error: transfer.error,
            metadata: transfer.metadata
        };
    }

    // 获取客户端传输
    getClientTransfers(clientId) {
        const clientTransferSet = this.clientTransfers.get(clientId);
        if (!clientTransferSet) {
            return [];
        }

        return Array.from(clientTransferSet).map(transferId => 
            this.getTransfer(transferId)
        ).filter(transfer => transfer !== null);
    }

    // 获取所有传输
    getAllTransfers() {
        return Array.from(this.transfers.keys()).map(transferId => 
            this.getTransfer(transferId)
        );
    }

    // 移除客户端
    removeClient(clientId) {
        const clientTransferSet = this.clientTransfers.get(clientId);
        if (clientTransferSet) {
            // 取消客户端的所有活跃传输
            for (const transferId of clientTransferSet) {
                const transfer = this.transfers.get(transferId);
                if (transfer && [
                    FILE_TRANSFER_STATUS.PENDING,
                    FILE_TRANSFER_STATUS.INITIALIZING,
                    FILE_TRANSFER_STATUS.TRANSFERRING,
                    FILE_TRANSFER_STATUS.VERIFYING
                ].includes(transfer.status)) {
                    this.cancelTransfer(transferId, '客户端断开连接');
                }
            }
            
            this.clientTransfers.delete(clientId);
        }

        console.log(`移除客户端文件传输: ${clientId}`);
    }

    // 获取统计信息
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalTransfers > 0 ? 
                (this.stats.completedTransfers / this.stats.totalTransfers * 100).toFixed(2) : 0,
            averageFileSize: this.stats.completedTransfers > 0 ? 
                (this.stats.totalBytesTransferred / this.stats.completedTransfers).toFixed(2) : 0,
            chunkStats: this.chunkProtocol.getStats()
        };
    }

    // 清理过期传输
    cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24小时
        const now = Date.now();
        let cleanedCount = 0;

        for (const [transferId, transfer] of this.transfers) {
            if (transfer.completedAt && (now - transfer.completedAt) > maxAge) {
                if ([
                    FILE_TRANSFER_STATUS.COMPLETED,
                    FILE_TRANSFER_STATUS.FAILED,
                    FILE_TRANSFER_STATUS.CANCELLED
                ].includes(transfer.status)) {
                    this.transfers.delete(transferId);
                    cleanedCount++;
                }
            }
        }

        // 清理分块协议的过期传输
        const chunkCleanedCount = this.chunkProtocol.cleanupExpiredTransfers(maxAge);

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期文件传输, ${chunkCleanedCount} 个过期分块传输`);
        }

        return cleanedCount;
    }
}

module.exports = {
    FileTransferManager,
    TRANSFER_DIRECTION,
    FILE_TRANSFER_STATUS
};