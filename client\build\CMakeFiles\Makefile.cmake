# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenSSL.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Linker/GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Linker/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-windres.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"
  "C:/Users/<USER>/Desktop/windows/client/CMakeLists.txt"
  "CMakeFiles/3.31.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.4/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.4/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.4/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/DistributedPluginClient.dir/DependInfo.cmake"
  )
