#pragma once

#include "common.h"
#include "config.h"

namespace Crypto {

    class XORCrypto {
    public:
        XOR<PERSON>rypto();
        explicit XORCrypto(const ByteArray& key);
        ~XORCrypto() = default;

        // Basic XOR encryption/decryption
        ByteArray Encrypt(const ByteArray& data) const;
        ByteArray Decrypt(const ByteArray& data) const;
        
        // String variants
        std::string EncryptString(const std::string& data) const;
        std::string DecryptString(const std::string& data) const;
        
        // Base64 encoded variants for text transmission
        std::string EncryptToBase64(const ByteArray& data) const;
        std::string EncryptStringToBase64(const std::string& data) const;
        ByteArray DecryptFromBase64(const std::string& base64_data) const;
        std::string DecryptStringFromBase64(const std::string& base64_data) const;
        
        // Chunked encryption for large data
        bool EncryptChunked(const ByteArray& input, ByteArray& output, size_t chunk_size = Config::CHUNK_SIZE) const;
        bool DecryptChunked(const ByteArray& input, ByteArray& output, size_t chunk_size = Config::CHUNK_SIZE) const;
        
        // Stream encryption for files
        bool EncryptFile(const std::string& input_path, const std::string& output_path) const;
        bool DecryptFile(const std::string& input_path, const std::string& output_path) const;
        
        // Key management
        void SetKey(const ByteArray& key);
        void SetKey(const uint8_t* key, size_t key_size);
        void SetDefaultKey();
        void GenerateRandomKey(size_t key_size = 32);
        
        ByteArray GetKey() const { return key_; }
        size_t GetKeySize() const { return key_.size(); }
        
        // Utility functions
        static ByteArray GenerateRandomBytes(size_t size);
        static std::string BytesToHex(const ByteArray& bytes);
        static ByteArray HexToBytes(const std::string& hex);
        static std::string Base64Encode(const ByteArray& data);
        static ByteArray Base64Decode(const std::string& base64_data);
        
        // Enhanced security variants
        ByteArray EncryptWithSalt(const ByteArray& data) const;
        ByteArray DecryptWithSalt(const ByteArray& encrypted_data) const;
        
    private:
        ByteArray key_;
        
        void XOROperation(const uint8_t* input, uint8_t* output, size_t data_size) const;
        void ExpandKey(size_t target_size);
        
        // Enhanced XOR with key scheduling
        ByteArray GenerateKeyStream(size_t length, uint64_t nonce = 0) const;
        void KeySchedule(const ByteArray& original_key, ByteArray& scheduled_key, size_t rounds = 16) const;
    };

} // namespace Crypto