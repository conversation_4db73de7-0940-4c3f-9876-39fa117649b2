#include "simple_websocket_client.h"
#include <random>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <netdb.h>
#endif

namespace Network {

// Simple Base64 implementation without OpenSSL
class Base64 {
public:
    static std::string encode(const std::vector<uint8_t>& data) {
        static const char chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int i = 0;
        uint8_t char_array_3[3];
        uint8_t char_array_4[4];

        for (auto byte : data) {
            char_array_3[i++] = byte;
            if (i == 3) {
                char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
                char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
                char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
                char_array_4[3] = char_array_3[2] & 0x3f;

                for (i = 0; i < 4; i++) {
                    result += chars[char_array_4[i]];
                }
                i = 0;
            }
        }

        if (i) {
            for (int j = 0; j < i; j++) {
                char_array_4[j] = (char_array_3[j] & 0xfc) >> 2;
            }

            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);

            for (int j = 0; j < i + 1; j++) {
                result += chars[char_array_4[j]];
            }

            while (i++ < 3) {
                result += '=';
            }
        }

        return result;
    }
};

// Simple SHA-1 implementation (simplified for demo)
class SimpleSHA1 {
public:
    static std::string hash(const std::string& input) {
        // This is a simplified hash for demo purposes
        // In production, use a proper SHA-1 implementation
        uint32_t hash_value = 0;
        for (char c : input) {
            hash_value = hash_value * 31 + static_cast<uint32_t>(c);
        }
        
        std::stringstream ss;
        ss << std::hex << hash_value;
        std::string result = ss.str();
        
        // Pad to 40 characters (SHA-1 length)
        while (result.length() < 40) {
            result = "0" + result;
        }
        
        return result;
    }
};

SimpleWebSocketClient::SimpleWebSocketClient()
    : socket_(INVALID_SOCKET)
    , state_(ConnectionState::DISCONNECTED)
    , should_reconnect_(false)
    , should_stop_(false)
    , heartbeat_enabled_(false)
    , server_port_(8080)
    , server_path_("/")
    , reconnect_attempts_(0) {
    
#ifdef _WIN32
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        LOG_ERROR("WSAStartup failed: " + std::to_string(result));
    }
#endif
    
    LOG_INFO("Simple WebSocket client initialized");
}

SimpleWebSocketClient::~SimpleWebSocketClient() {
    Stop();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

bool SimpleWebSocketClient::Connect(const std::string& host, uint16_t port, const std::string& path) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (state_ == ConnectionState::CONNECTED || state_ == ConnectionState::CONNECTING) {
        LOG_WARNING("Already connected or connecting");
        return false;
    }
    
    server_host_ = host;
    server_port_ = port;
    server_path_ = path;
    server_uri_ = BuildUri(host, port, path);
    
    SetState(ConnectionState::CONNECTING);
    
    // Create socket
#ifdef _WIN32
    socket_ = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (socket_ == INVALID_SOCKET) {
        LOG_ERROR("Failed to create socket: " + std::to_string(WSAGetLastError()));
        SetState(ConnectionState::FAILED);
        return false;
    }
#else
    socket_ = socket(AF_INET, SOCK_STREAM, 0);
    if (socket_ < 0) {
        LOG_ERROR("Failed to create socket");
        SetState(ConnectionState::FAILED);
        return false;
    }
#endif
    
    // Resolve hostname
    struct hostent* host_entry = gethostbyname(host.c_str());
    if (!host_entry) {
        LOG_ERROR("Failed to resolve hostname: " + host);
        SetState(ConnectionState::FAILED);
        return false;
    }
    
    // Connect to server
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    memcpy(&server_addr.sin_addr, host_entry->h_addr_list[0], host_entry->h_length);
    
#ifdef _WIN32
    int result = connect(socket_, (struct sockaddr*)&server_addr, sizeof(server_addr));
    if (result == SOCKET_ERROR) {
        LOG_ERROR("Failed to connect: " + std::to_string(WSAGetLastError()));
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        SetState(ConnectionState::FAILED);
        return false;
    }
#else
    int result = connect(socket_, (struct sockaddr*)&server_addr, sizeof(server_addr));
    if (result < 0) {
        LOG_ERROR("Failed to connect");
        close(socket_);
        socket_ = -1;
        SetState(ConnectionState::FAILED);
        return false;
    }
#endif
    
    // Perform WebSocket handshake
    if (!PerformHandshake()) {
        LOG_ERROR("WebSocket handshake failed");
        SetState(ConnectionState::FAILED);
        return false;
    }
    
    SetState(ConnectionState::CONNECTED);
    OnOpen();
    
    // Start network thread
    network_thread_ = std::thread(&SimpleWebSocketClient::NetworkThread, this);
    
    LOG_INFO("Connected to: " + server_uri_);
    return true;
}

void SimpleWebSocketClient::Disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    should_stop_ = true;
    
    if (socket_ != INVALID_SOCKET) {
        // Send close frame
        std::vector<uint8_t> close_frame = CreateWebSocketFrame("", false);
        close_frame[0] = (close_frame[0] & 0xF0) | WS_OPCODE_CLOSE;
        SendRawData(close_frame);
        
#ifdef _WIN32
        closesocket(socket_);
#else
        close(socket_);
#endif
        socket_ = INVALID_SOCKET;
    }
    
    if (network_thread_.joinable()) {
        network_thread_.join();
    }
    
    SetState(ConnectionState::DISCONNECTED);
    OnClose();
}

bool SimpleWebSocketClient::Send(const std::string& message) {
    std::lock_guard<std::mutex> lock(send_mutex_);
    
    if (state_ != ConnectionState::CONNECTED) {
        LOG_ERROR("Not connected");
        return false;
    }
    
    std::vector<uint8_t> frame = CreateWebSocketFrame(message, true);
    bool success = SendRawData(frame);
    
    if (success) {
        LOG_DEBUG("Sent message: " + message.substr(0, 100) + (message.length() > 100 ? "..." : ""));
    }
    
    return success;
}

bool SimpleWebSocketClient::SendBinary(const ByteArray& data) {
    std::lock_guard<std::mutex> lock(send_mutex_);
    
    if (state_ != ConnectionState::CONNECTED) {
        LOG_ERROR("Not connected");
        return false;
    }
    
    std::string data_str(data.begin(), data.end());
    std::vector<uint8_t> frame = CreateWebSocketFrame(data_str, false);
    return SendRawData(frame);
}

void SimpleWebSocketClient::Run() {
    LOG_INFO("WebSocket client started");
    StartReconnectLoop();
    StartHeartbeat();
}

void SimpleWebSocketClient::Stop() {
    LOG_INFO("WebSocket client stopping");
    should_stop_ = true;
    StopReconnectLoop();
    StopHeartbeat();
    Disconnect();
    LOG_INFO("WebSocket client stopped");
}

void SimpleWebSocketClient::SetState(ConnectionState new_state) {
    ConnectionState old_state = state_.exchange(new_state);
    if (old_state != new_state) {
        LOG_INFO("Connection state changed: " + std::to_string(static_cast<int>(old_state)) + 
                " -> " + std::to_string(static_cast<int>(new_state)));
        if (state_callback_) {
            state_callback_(new_state);
        }
    }
}

void SimpleWebSocketClient::OnOpen() {
    LOG_INFO("WebSocket connection opened");
    reconnect_attempts_ = 0;
}

void SimpleWebSocketClient::OnClose() {
    LOG_INFO("WebSocket connection closed");
}

void SimpleWebSocketClient::OnFail() {
    LOG_ERROR("WebSocket connection failed");
}

void SimpleWebSocketClient::OnMessage(const std::string& message) {
    LOG_DEBUG("Received message: " + message.substr(0, 100) + (message.length() > 100 ? "..." : ""));
    
    if (message_callback_) {
        message_callback_(message);
    }
}

bool SimpleWebSocketClient::PerformHandshake() {
    // Generate WebSocket key
    std::string ws_key = GenerateWebSocketKey();
    
    // Create HTTP upgrade request
    std::ostringstream request;
    request << "GET " << server_path_ << " HTTP/1.1\r\n";
    request << "Host: " << server_host_ << ":" << server_port_ << "\r\n";
    request << "Upgrade: websocket\r\n";
    request << "Connection: Upgrade\r\n";
    request << "Sec-WebSocket-Key: " << ws_key << "\r\n";
    request << "Sec-WebSocket-Version: 13\r\n";
    request << "User-Agent: DistributedPluginClient/1.0\r\n";
    request << "\r\n";
    
    std::string request_str = request.str();
    std::vector<uint8_t> request_data(request_str.begin(), request_str.end());
    
    LOG_DEBUG("Sending handshake request: " + request_str);
    
    if (!SendRawData(request_data)) {
        LOG_ERROR("Failed to send handshake request");
        return false;
    }
    
    // Receive response
    std::vector<uint8_t> response = ReceiveRawData();
    if (response.empty()) {
        LOG_ERROR("Failed to receive handshake response");
        return false;
    }
    
    std::string response_str(response.begin(), response.end());
    LOG_DEBUG("Handshake response: " + response_str);
    
    // Check for successful handshake
    if (response_str.find("HTTP/1.1 101") == std::string::npos &&
        response_str.find("HTTP/1.0 101") == std::string::npos) {
        LOG_ERROR("Handshake failed - invalid response code");
        return false;
    }
    
    if (response_str.find("Upgrade: websocket") == std::string::npos &&
        response_str.find("upgrade: websocket") == std::string::npos) {
        LOG_ERROR("Handshake failed - missing upgrade header");
        return false;
    }
    
    LOG_INFO("WebSocket handshake successful");
    return true;
}

bool SimpleWebSocketClient::SendRawData(const std::vector<uint8_t>& data) {
    if (socket_ == INVALID_SOCKET) {
        return false;
    }
    
#ifdef _WIN32
    int sent = send(socket_, reinterpret_cast<const char*>(data.data()), static_cast<int>(data.size()), 0);
    return sent == static_cast<int>(data.size());
#else
    ssize_t sent = send(socket_, data.data(), data.size(), 0);
    return sent == static_cast<ssize_t>(data.size());
#endif
}

std::vector<uint8_t> SimpleWebSocketClient::ReceiveRawData() {
    std::vector<uint8_t> buffer(4096);
    
#ifdef _WIN32
    int received = recv(socket_, reinterpret_cast<char*>(buffer.data()), static_cast<int>(buffer.size()), 0);
    if (received > 0) {
        buffer.resize(received);
        return buffer;
    }
#else
    ssize_t received = recv(socket_, buffer.data(), buffer.size(), 0);
    if (received > 0) {
        buffer.resize(received);
        return buffer;
    }
#endif
    
    return std::vector<uint8_t>();
}

std::vector<uint8_t> SimpleWebSocketClient::CreateWebSocketFrame(const std::string& message, bool is_text) {
    std::vector<uint8_t> frame;
    
    // First byte: FIN + opcode
    uint8_t first_byte = WS_FIN | (is_text ? WS_OPCODE_TEXT : WS_OPCODE_BINARY);
    frame.push_back(first_byte);
    
    // Payload length
    size_t payload_length = message.length();
    if (payload_length < 126) {
        frame.push_back(static_cast<uint8_t>(payload_length) | WS_MASK);
    } else if (payload_length < 65536) {
        frame.push_back(126 | WS_MASK);
        frame.push_back(static_cast<uint8_t>(payload_length >> 8));
        frame.push_back(static_cast<uint8_t>(payload_length & 0xFF));
    } else {
        frame.push_back(127 | WS_MASK);
        for (int i = 7; i >= 0; i--) {
            frame.push_back(static_cast<uint8_t>((payload_length >> (i * 8)) & 0xFF));
        }
    }
    
    // Masking key
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    uint8_t mask[4];
    for (int i = 0; i < 4; i++) {
        mask[i] = static_cast<uint8_t>(dis(gen));
        frame.push_back(mask[i]);
    }
    
    // Masked payload
    for (size_t i = 0; i < payload_length; i++) {
        frame.push_back(static_cast<uint8_t>(message[i] ^ mask[i % 4]));
    }
    
    return frame;
}

std::string SimpleWebSocketClient::ParseWebSocketFrame(const std::vector<uint8_t>& frame) {
    if (frame.size() < 2) {
        return "";
    }
    
    uint8_t opcode = frame[0] & 0x0F;
    bool masked = (frame[1] & WS_MASK) != 0;
    
    if (opcode == WS_OPCODE_CLOSE) {
        LOG_INFO("Received close frame");
        return "";
    }
    
    if (opcode == WS_OPCODE_PING) {
        LOG_DEBUG("Received ping frame");
        // Should send pong response
        return "";
    }
    
    if (opcode == WS_OPCODE_PONG) {
        LOG_DEBUG("Received pong frame");
        return "";
    }
    
    if (opcode != WS_OPCODE_TEXT && opcode != WS_OPCODE_BINARY) {
        LOG_WARNING("Unknown opcode: " + std::to_string(opcode));
        return "";
    }
    
    size_t payload_start = 2;
    size_t payload_length = frame[1] & 0x7F;
    
    if (payload_length == 126) {
        if (frame.size() < 4) return "";
        payload_length = (frame[2] << 8) | frame[3];
        payload_start = 4;
    } else if (payload_length == 127) {
        if (frame.size() < 10) return "";
        payload_length = 0;
        for (int i = 0; i < 8; i++) {
            payload_length = (payload_length << 8) | frame[2 + i];
        }
        payload_start = 10;
    }
    
    if (masked) {
        payload_start += 4;
    }
    
    if (frame.size() < payload_start + payload_length) {
        LOG_WARNING("Incomplete frame received");
        return "";
    }
    
    std::string result;
    result.resize(payload_length);
    
    if (masked) {
        uint8_t mask[4] = {frame[payload_start - 4], frame[payload_start - 3], 
                          frame[payload_start - 2], frame[payload_start - 1]};
        for (size_t i = 0; i < payload_length; i++) {
            result[i] = frame[payload_start + i] ^ mask[i % 4];
        }
    } else {
        for (size_t i = 0; i < payload_length; i++) {
            result[i] = frame[payload_start + i];
        }
    }
    
    return result;
}

void SimpleWebSocketClient::NetworkThread() {
    LOG_INFO("Network thread started");
    
    while (!should_stop_ && state_ == ConnectionState::CONNECTED) {
        std::vector<uint8_t> data = ReceiveRawData();
        if (!data.empty()) {
            std::string message = ParseWebSocketFrame(data);
            if (!message.empty()) {
                OnMessage(message);
            }
        } else {
            // Connection lost
            LOG_WARNING("Connection lost - no data received");
            SetState(ConnectionState::DISCONNECTED);
            OnClose();
            break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    LOG_INFO("Network thread ended");
}

void SimpleWebSocketClient::StartReconnectLoop() {
    should_reconnect_ = true;
    reconnect_thread_ = std::thread(&SimpleWebSocketClient::ReconnectTask, this);
    LOG_INFO("Reconnect loop started");
}

void SimpleWebSocketClient::StopReconnectLoop() {
    should_reconnect_ = false;
    if (reconnect_thread_.joinable()) {
        reconnect_thread_.join();
    }
    LOG_INFO("Reconnect loop stopped");
}

void SimpleWebSocketClient::ReconnectTask() {
    while (should_reconnect_ && !should_stop_) {
        if (state_ == ConnectionState::DISCONNECTED || state_ == ConnectionState::FAILED) {
            if (reconnect_attempts_ < Config::MAX_RECONNECT_ATTEMPTS) {
                LOG_INFO("Attempting to reconnect... (attempt " + std::to_string(reconnect_attempts_ + 1) + ")");
                if (Connect(server_host_, server_port_, server_path_)) {
                    reconnect_attempts_ = 0;
                } else {
                    reconnect_attempts_++;
                    std::this_thread::sleep_for(std::chrono::milliseconds(Config::RECONNECT_INTERVAL_MS));
                }
            } else {
                LOG_ERROR("Max reconnect attempts reached");
                should_reconnect_ = false;
            }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void SimpleWebSocketClient::StartHeartbeat() {
    heartbeat_enabled_ = true;
    heartbeat_thread_ = std::thread(&SimpleWebSocketClient::SendHeartbeat, this);
    LOG_INFO("Heartbeat started");
}

void SimpleWebSocketClient::StopHeartbeat() {
    heartbeat_enabled_ = false;
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    LOG_INFO("Heartbeat stopped");
}

void SimpleWebSocketClient::SendHeartbeat() {
    last_heartbeat_ = std::chrono::steady_clock::now();
    
    while (heartbeat_enabled_ && !should_stop_) {
        if (state_ == ConnectionState::CONNECTED) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_heartbeat_);
            
            if (elapsed.count() >= Config::HEARTBEAT_INTERVAL_MS / 1000) {
                // Send ping frame
                std::vector<uint8_t> ping_frame = CreateWebSocketFrame("", true);
                ping_frame[0] = (ping_frame[0] & 0xF0) | WS_OPCODE_PING;
                if (SendRawData(ping_frame)) {
                    last_heartbeat_ = now;
                    LOG_DEBUG("Heartbeat sent");
                } else {
                    LOG_ERROR("Failed to send heartbeat");
                }
            }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

std::string SimpleWebSocketClient::GenerateWebSocketKey() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    std::vector<uint8_t> key(16);
    for (int i = 0; i < 16; i++) {
        key[i] = static_cast<uint8_t>(dis(gen));
    }
    
    return Base64::encode(key);
}

std::string SimpleWebSocketClient::GenerateAcceptKey(const std::string& key) {
    std::string magic = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11";
    std::string combined = key + magic;
    
    // Use simplified hash for demo
    std::string hash = SimpleSHA1::hash(combined);
    
    // Convert hex string to bytes and base64 encode
    std::vector<uint8_t> hash_bytes;
    for (size_t i = 0; i < hash.length(); i += 2) {
        std::string byte_str = hash.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16));
        hash_bytes.push_back(byte);
    }
    
    return Base64::encode(hash_bytes);
}

std::string SimpleWebSocketClient::GenerateClientId() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    ss << "client_" << std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count() << "_";
    
    for (int i = 0; i < 8; ++i) {
        ss << std::hex << dis(gen);
    }
    
    return ss.str();
}

std::string SimpleWebSocketClient::BuildUri(const std::string& host, uint16_t port, const std::string& path) {
    return "ws://" + host + ":" + std::to_string(port) + path;
}

} // namespace Network