const WebSocketServer = require('./src/core/websocket-server');
const ClientManager = require('./src/core/client-manager');
const CryptoUtils = require('./src/core/crypto-utils');
const { PluginManager } = require('./src/core/plugin-manager');
const { TaskScheduler } = require('./src/core/task-scheduler');
const { FileTransferManager } = require('./src/core/file-transfer');
const { MessageProtocol, MESSAGE_TYPES, ERROR_CODES } = require('./src/protocols/message-protocol');
const { ChunkProtocol } = require('./src/protocols/chunk-protocol');
const { defaultLogger } = require('./src/utils/logger');
const { defaultConfig } = require('./src/utils/config');

class DistributedPluginServer {
    constructor() {
        this.config = defaultConfig;
        this.logger = defaultLogger;
        this.wsServer = null;
        this.clientManager = null;
        this.pluginManager = null;
        this.taskScheduler = null;
        this.fileTransferManager = null;
        this.cryptoUtils = null;
        this.messageProtocol = null;
        this.chunkProtocol = null;
        this.isRunning = false;
        this.stats = {
            startTime: null,
            messagesProcessed: 0,
            errorsOccurred: 0,
            totalConnections: 0
        };
        
        this.initialize();
    }

    initialize() {
        try {
            // 从环境变量加载配置
            this.config.loadFromEnvironment();
            
            // 初始化日志系统
            this.setupLogger();
            
            // 初始化加密工具
            this.cryptoUtils = new CryptoUtils();
            
            // 初始化消息协议
            this.messageProtocol = new MessageProtocol();
            
            // 初始化分块协议
            this.chunkProtocol = new ChunkProtocol();
            
            // 初始化客户端管理器
            this.clientManager = new ClientManager();
            this.setupClientManagerEvents();
            
            // 初始化插件管理器
            this.pluginManager = new PluginManager();
            this.setupPluginManagerEvents();
            
            // 初始化任务调度器
            this.taskScheduler = new TaskScheduler();
            this.setupTaskSchedulerEvents();
            
            // 初始化文件传输管理器
            this.fileTransferManager = new FileTransferManager();
            this.setupFileTransferEvents();
            
            // 初始化WebSocket服务器
            this.setupWebSocketServer();
            
            this.logger.info('服务器初始化完成');
        } catch (error) {
            console.error('服务器初始化失败:', error);
            process.exit(1);
        }
    }

    setupLogger() {
        const logConfig = this.config.get('logging');
        
        this.logger.setLevel(logConfig.level);
        this.logger.setConsoleOutput(logConfig.enableConsole);
        this.logger.setFileOutput(logConfig.enableFile);
        
        if (logConfig.enableFile) {
            this.logger.logDirectory = logConfig.logDirectory;
            this.logger.maxFileSize = logConfig.maxFileSize;
            this.logger.maxFiles = logConfig.maxFiles;
        }
    }

    setupWebSocketServer() {
        const serverConfig = this.config.get('server');
        
        this.wsServer = new WebSocketServer({
            port: serverConfig.port,
            maxConnections: serverConfig.maxConnections,
            heartbeatInterval: serverConfig.heartbeatInterval
        });
        
        this.setupWebSocketEvents();
    }

    setupWebSocketEvents() {
        this.wsServer.on('clientConnected', (clientInfo) => {
            this.handleClientConnected(clientInfo);
        });
        
        this.wsServer.on('clientDisconnected', (clientInfo, code, reason) => {
            this.handleClientDisconnected(clientInfo, code, reason);
        });
        
        this.wsServer.on('message', (clientId, message) => {
            this.handleMessage(clientId, message);
        });
    }

    setupClientManagerEvents() {
        this.clientManager.on('clientAdded', (client) => {
            this.logger.info(`客户端已添加到管理器: ${client.id}`, {
                ip: client.ip,
                userAgent: client.userAgent
            });
        });
        
        this.clientManager.on('clientRemoved', (client) => {
            this.logger.info(`客户端已从管理器移除: ${client.id}`);
        });
        
        this.clientManager.on('clientStatusChanged', (client, oldStatus, newStatus) => {
            this.logger.info(`客户端状态变更: ${client.id} (${oldStatus} -> ${newStatus})`);
        });
    }

    setupPluginManagerEvents() {
        this.pluginManager.on('pluginAdded', (plugin) => {
            this.logger.info(`插件已添加: ${plugin.name} v${plugin.version}`);
        });
        
        this.pluginManager.on('distributionStarted', (distribution) => {
            this.logger.info(`开始插件分发: ${distribution.pluginName} -> ${distribution.clientId}`);
        });
        
        this.pluginManager.on('distributionCompleted', (distribution) => {
            this.logger.info(`插件分发完成: ${distribution.pluginName} (${distribution.status})`);
        });
    }

    setupTaskSchedulerEvents() {
        this.taskScheduler.on('taskCreated', (task) => {
            this.logger.info(`任务已创建: ${task.name} (${task.id})`);
        });
        
        this.taskScheduler.on('taskCompleted', (task) => {
            this.logger.info(`任务已完成: ${task.name} (${task.id})`);
        });
        
        this.taskScheduler.on('taskFailed', (task, reason) => {
            this.logger.error(`任务失败: ${task.name} (${task.id}) - ${reason}`);
        });
    }

    setupFileTransferEvents() {
        this.fileTransferManager.on('transferCompleted', (transfer) => {
            this.logger.info(`文件传输完成: ${transfer.originalName} (${transfer.direction})`);
        });
        
        this.fileTransferManager.on('transferFailed', (transfer, reason) => {
            this.logger.error(`文件传输失败: ${transfer.originalName} - ${reason}`);
        });
        
        this.fileTransferManager.on('chunkMessage', (clientId, message) => {
            // 转发分块消息到客户端
            this.sendMessage(clientId, message);
        });
        
        this.fileTransferManager.on('requestFileData', (clientId, request) => {
            // 请求客户端发送文件数据
            this.sendMessage(clientId, request);
        });
    }

    handleClientConnected(clientInfo) {
        try {
            // 添加到客户端管理器
            const client = this.clientManager.addClient(clientInfo);
            
            // 更新统计信息
            this.stats.totalConnections++;
            
            this.logger.info(`新客户端连接: ${clientInfo.id}`, {
                ip: clientInfo.ip,
                totalConnections: this.stats.totalConnections,
                currentConnections: this.clientManager.getStats().currentConnections
            });
            
        } catch (error) {
            this.logger.error(`处理客户端连接失败: ${clientInfo.id}`, { error: error.message });
            this.stats.errorsOccurred++;
        }
    }

    handleClientDisconnected(clientInfo, code, reason) {
        try {
            // 从客户端管理器移除
            this.clientManager.removeClient(clientInfo.id);
            
            // 清理插件管理器中的客户端数据
            this.pluginManager.removeClient(clientInfo.id);
            
            // 清理任务调度器中的客户端任务
            this.taskScheduler.removeClient(clientInfo.id);
            
            // 清理文件传输管理器中的客户端传输
            this.fileTransferManager.removeClient(clientInfo.id);
            
            this.logger.info(`客户端断开连接: ${clientInfo.id}`, {
                code: code,
                reason: reason,
                currentConnections: this.clientManager.getStats().currentConnections
            });
            
        } catch (error) {
            this.logger.error(`处理客户端断开失败: ${clientInfo.id}`, { error: error.message });
            this.stats.errorsOccurred++;
        }
    }

    handleMessage(clientId, message) {
        try {
            // 更新客户端活动时间
            this.clientManager.updateClientActivity(clientId);
            
            // 更新统计信息
            this.stats.messagesProcessed++;
            
            this.logger.debug(`收到消息: ${clientId}`, {
                type: message.type,
                messageId: message.id
            });
            
            // 处理加密消息
            if (message.encrypted && this.config.get('security.enableEncryption')) {
                message = this.decryptMessage(message);
            }
            
            // 根据消息类型处理
            this.routeMessage(clientId, message);
            
        } catch (error) {
            this.logger.error(`处理消息失败: ${clientId}`, {
                error: error.message,
                messageType: message.type,
                messageId: message.id
            });
            
            this.stats.errorsOccurred++;
            
            // 发送错误响应
            this.sendError(clientId, ERROR_CODES.MESSAGE_PROCESSING_ERROR, '消息处理失败');
        }
    }

    routeMessage(clientId, message) {
        switch (message.type) {
            case MESSAGE_TYPES.HEARTBEAT:
                this.handleHeartbeat(clientId, message);
                break;
                
            case MESSAGE_TYPES.AUTH_REQUEST:
                this.handleAuthRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.SYSTEM_INFO_REQUEST:
                this.handleSystemInfoRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.CLIENT_LIST_REQUEST:
                this.handleClientListRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.PLUGIN_LIST_REQUEST:
                this.handlePluginListRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.PLUGIN_DOWNLOAD_REQUEST:
                this.handlePluginDownloadRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.PLUGIN_EXECUTE_REQUEST:
                this.handlePluginExecuteRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.PLUGIN_EXECUTE_RESPONSE:
                this.handlePluginExecuteResponse(clientId, message);
                break;
                
            case MESSAGE_TYPES.TASK_STATUS_UPDATE:
                this.handleTaskStatusUpdate(clientId, message);
                break;
                
            case MESSAGE_TYPES.TASK_RESULT:
                this.handleTaskResult(clientId, message);
                break;
                
            case MESSAGE_TYPES.FILE_TRANSFER:
                this.handleFileTransferRequest(clientId, message);
                break;
                
            case MESSAGE_TYPES.CHUNK_DATA:
                this.handleChunkData(clientId, message);
                break;
                
            case MESSAGE_TYPES.CHUNK_ACK:
                this.handleChunkAck(clientId, message);
                break;
                
            default:
                this.logger.warn(`未知消息类型: ${message.type}`, { clientId });
                this.sendError(clientId, ERROR_CODES.INVALID_MESSAGE_TYPE, `未知消息类型: ${message.type}`);
        }
    }

    handleHeartbeat(clientId, message) {
        const response = this.messageProtocol.createHeartbeatResponse(message);
        this.sendMessage(clientId, response);
    }

    handleAuthRequest(clientId, message) {
        // 简单的认证实现 - 在实际应用中应该使用更安全的认证方法
        const authEnabled = this.config.get('security.enableAuthentication');
        
        if (!authEnabled) {
            const response = this.messageProtocol.createAuthResponse(true, 'dummy_token');
            this.sendMessage(clientId, response);
            this.clientManager.setClientStatus(clientId, 'authenticated');
            return;
        }
        
        // 这里应该实现真正的认证逻辑
        const response = this.messageProtocol.createAuthResponse(false, null, '认证功能未实现');
        this.sendMessage(clientId, response);
    }

    handleSystemInfoRequest(clientId, message) {
        const systemInfo = {
            serverVersion: '1.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            cpuUsage: process.cpuUsage(),
            serverStats: this.getServerStats(),
            timestamp: Date.now()
        };
        
        const response = this.messageProtocol.createSuccessResponse(message, {
            systemInfo: systemInfo
        });
        
        this.sendMessage(clientId, response);
    }

    handleClientListRequest(clientId, message) {
        const clients = this.clientManager.getAllClients();
        const response = this.messageProtocol.createSuccessResponse(message, {
            clients: clients,
            totalCount: clients.length
        });
        
        this.sendMessage(clientId, response);
    }

    handlePluginListRequest(clientId, message) {
        try {
            const plugins = this.pluginManager.getPluginList(clientId);
            const response = this.messageProtocol.createSuccessResponse(message, {
                plugins: plugins,
                totalCount: plugins.length
            });
            
            this.sendMessage(clientId, response);
        } catch (error) {
            this.logger.error(`处理插件列表请求失败: ${clientId}`, { error: error.message });
            this.sendError(clientId, ERROR_CODES.INTERNAL_SERVER_ERROR, '获取插件列表失败');
        }
    }

    async handlePluginDownloadRequest(clientId, message) {
        try {
            const { pluginName } = message.data;
            
            // 检查插件是否存在
            const plugin = this.pluginManager.getPlugin(pluginName);
            if (!plugin) {
                this.sendError(clientId, ERROR_CODES.PLUGIN_NOT_FOUND, `插件不存在: ${pluginName}`);
                return;
            }
            
            // 开始分发插件
            const distributionId = await this.pluginManager.distributePlugin(pluginName, clientId);
            
            // 获取插件数据并使用文件传输系统发送
            const pluginData = await this.pluginManager.getPluginData(pluginName);
            const transferId = await this.fileTransferManager.initializeDownload(
                clientId, 
                null, // 不是从文件路径
                plugin.filename
            );
            
            // 开始传输
            await this.fileTransferManager.receiveUploadData(transferId, pluginData);
            
            const response = this.messageProtocol.createSuccessResponse(message, {
                distributionId: distributionId,
                transferId: transferId,
                plugin: {
                    name: plugin.name,
                    version: plugin.version,
                    size: plugin.size,
                    checksum: plugin.hash
                }
            });
            
            this.sendMessage(clientId, response);
            
        } catch (error) {
            this.logger.error(`处理插件下载请求失败: ${clientId}`, { error: error.message });
            this.sendError(clientId, ERROR_CODES.PLUGIN_EXECUTION_FAILED, '插件下载失败');
        }
    }

    handlePluginExecuteRequest(clientId, message) {
        try {
            const { pluginName, command, parameters } = message.data;
            
            // 创建插件执行会话
            const sessionId = this.pluginManager.createExecutionSession(
                pluginName, clientId, command, parameters
            );
            
            // 创建任务
            const taskId = this.taskScheduler.createTask({
                type: 'plugin_execute',
                name: `执行插件: ${pluginName}.${command}`,
                payload: {
                    pluginName: pluginName,
                    command: command,
                    parameters: parameters,
                    sessionId: sessionId
                },
                targetClients: [clientId],
                priority: 'normal'
            });
            
            // 调度任务
            this.taskScheduler.scheduleTask(taskId);
            
            const response = this.messageProtocol.createSuccessResponse(message, {
                sessionId: sessionId,
                taskId: taskId
            });
            
            this.sendMessage(clientId, response);
            
        } catch (error) {
            this.logger.error(`处理插件执行请求失败: ${clientId}`, { error: error.message });
            this.sendError(clientId, ERROR_CODES.PLUGIN_EXECUTION_FAILED, '插件执行请求失败');
        }
    }

    handlePluginExecuteResponse(clientId, message) {
        try {
            const { sessionId, success, result, error } = message.data;
            
            // 更新执行会话
            const status = success ? 'completed' : 'failed';
            this.pluginManager.updateExecutionSession(sessionId, status, result, error);
            
            this.logger.info(`插件执行响应: ${sessionId}`, { success, clientId });
            
        } catch (error) {
            this.logger.error(`处理插件执行响应失败: ${clientId}`, { error: error.message });
        }
    }

    handleTaskStatusUpdate(clientId, message) {
        try {
            const { taskId, status, progress, message: statusMessage } = message.data;
            
            // 更新任务进度
            this.taskScheduler.updateTaskProgress(taskId, clientId, progress, statusMessage);
            
            this.logger.debug(`任务状态更新: ${taskId}`, { status, progress, clientId });
            
        } catch (error) {
            this.logger.error(`处理任务状态更新失败: ${clientId}`, { error: error.message });
        }
    }

    handleTaskResult(clientId, message) {
        try {
            const { taskId, success, result, error } = message.data;
            
            // 提交任务结果
            this.taskScheduler.submitTaskResult(taskId, clientId, success, result, error);
            
            this.logger.info(`任务结果: ${taskId}`, { success, clientId });
            
        } catch (error) {
            this.logger.error(`处理任务结果失败: ${clientId}`, { error: error.message });
        }
    }

    async handleFileTransferRequest(clientId, message) {
        try {
            const { direction, fileName, fileSize, checksum } = message.data;
            
            if (direction === 'upload') {
                // 处理文件上传请求
                const transferId = await this.fileTransferManager.initializeUpload(clientId, {
                    fileName: fileName,
                    fileSize: fileSize,
                    checksum: checksum
                });
                
                await this.fileTransferManager.startUpload(transferId);
                
                const response = this.messageProtocol.createSuccessResponse(message, {
                    transferId: transferId,
                    status: 'ready'
                });
                
                this.sendMessage(clientId, response);
                
            } else {
                this.sendError(clientId, ERROR_CODES.INVALID_PARAMETERS, '暂不支持的传输方向');
            }
            
        } catch (error) {
            this.logger.error(`处理文件传输请求失败: ${clientId}`, { error: error.message });
            this.sendError(clientId, ERROR_CODES.INTERNAL_SERVER_ERROR, '文件传输请求失败');
        }
    }

    handleChunkData(clientId, message) {
        try {
            // 处理分块数据，转发给文件传输管理器
            this.fileTransferManager.handleChunkAck(message);
            
        } catch (error) {
            this.logger.error(`处理分块数据失败: ${clientId}`, { error: error.message });
        }
    }

    handleChunkAck(clientId, message) {
        try {
            // 处理分块确认，转发给文件传输管理器
            this.fileTransferManager.handleChunkAck(message);
            
        } catch (error) {
            this.logger.error(`处理分块确认失败: ${clientId}`, { error: error.message });
        }
    }

    sendMessage(clientId, message) {
        try {
            // 如果启用加密，加密消息
            if (this.config.get('security.enableEncryption')) {
                message = this.encryptMessage(message);
            }
            
            const success = this.wsServer.sendToClient(clientId, message);
            if (!success) {
                this.logger.warn(`发送消息失败: ${clientId}`, { messageType: message.type });
            }
            
        } catch (error) {
            this.logger.error(`发送消息异常: ${clientId}`, { error: error.message });
        }
    }

    sendError(clientId, errorCode, errorMessage) {
        const errorMsg = this.messageProtocol.createErrorMessage(errorCode, errorMessage);
        this.sendMessage(clientId, errorMsg);
    }

    encryptMessage(message) {
        try {
            message.encrypted = true;
            message.data = this.cryptoUtils.encryptJson(message.data);
            return message;
        } catch (error) {
            this.logger.error('消息加密失败', { error: error.message });
            throw error;
        }
    }

    decryptMessage(message) {
        try {
            message.data = this.cryptoUtils.decryptJson(message.data);
            message.encrypted = false;
            return message;
        } catch (error) {
            this.logger.error('消息解密失败', { error: error.message });
            throw error;
        }
    }

    getServerStats() {
        const clientStats = this.clientManager.getStats();
        const pluginStats = this.pluginManager.getStats();
        const taskStats = this.taskScheduler.getStats();
        const fileStats = this.fileTransferManager.getStats();
        const currentTime = Date.now();
        
        return {
            uptime: this.stats.startTime ? currentTime - this.stats.startTime : 0,
            messagesProcessed: this.stats.messagesProcessed,
            errorsOccurred: this.stats.errorsOccurred,
            totalConnections: this.stats.totalConnections,
            currentConnections: clientStats.currentConnections,
            maxConcurrentConnections: clientStats.maxConcurrentConnections,
            averageConnectionTime: clientStats.averageConnectionTime,
            plugins: {
                totalPlugins: pluginStats.totalPlugins,
                activeDistributions: pluginStats.activeDistributions,
                totalDistributions: pluginStats.totalDistributions,
                distributionSuccessRate: pluginStats.distributionSuccessRate
            },
            tasks: {
                totalTasks: taskStats.totalTasks,
                executingTasks: taskStats.executingTasksCount,
                completedTasks: taskStats.completedTasks,
                failedTasks: taskStats.failedTasks,
                successRate: taskStats.successRate
            },
            fileTransfers: {
                totalTransfers: fileStats.totalTransfers,
                activeTransfers: fileStats.activeTransfers,
                completedTransfers: fileStats.completedTransfers,
                totalBytesTransferred: fileStats.totalBytesTransferred,
                successRate: fileStats.successRate
            },
            memoryUsage: process.memoryUsage(),
            configSummary: this.config.getSummary()
        };
    }

    async start() {
        try {
            if (this.isRunning) {
                this.logger.warn('服务器已在运行中');
                return;
            }
            
            this.logger.info('正在启动分布式插件管理服务器...');
            
            // 初始化插件管理器
            await this.pluginManager.initialize();
            
            // 初始化文件传输管理器
            await this.fileTransferManager.initialize();
            
            // 启动任务调度器
            this.taskScheduler.start();
            
            // 启动WebSocket服务器
            await this.wsServer.start();
            
            this.isRunning = true;
            this.stats.startTime = Date.now();
            
            this.logger.info('=== 分布式插件管理服务器启动成功 ===', {
                port: this.config.get('server.port'),
                maxConnections: this.config.get('server.maxConnections'),
                encryptionEnabled: this.config.get('security.enableEncryption'),
                authenticationEnabled: this.config.get('security.enableAuthentication')
            });
            
            // 定期清理和统计
            this.startPeriodicTasks();
            
        } catch (error) {
            this.logger.error('服务器启动失败', { error: error.message });
            throw error;
        }
    }

    async stop() {
        try {
            if (!this.isRunning) {
                this.logger.warn('服务器未在运行');
                return;
            }
            
            this.logger.info('正在关闭服务器...');
            
            // 停止定期任务
            this.stopPeriodicTasks();
            
            // 停止任务调度器
            if (this.taskScheduler) {
                this.taskScheduler.stop();
            }
            
            // 关闭WebSocket服务器
            if (this.wsServer) {
                await this.wsServer.stop();
            }
            
            this.isRunning = false;
            
            this.logger.info('服务器已关闭', {
                uptime: Date.now() - this.stats.startTime,
                messagesProcessed: this.stats.messagesProcessed,
                totalConnections: this.stats.totalConnections
            });
            
        } catch (error) {
            this.logger.error('服务器关闭失败', { error: error.message });
            throw error;
        }
    }

    startPeriodicTasks() {
        // 每分钟清理超时客户端和过期数据
        this.cleanupTimer = setInterval(() => {
            this.clientManager.cleanup();
            this.pluginManager.cleanup();
            this.taskScheduler.cleanup();
            this.fileTransferManager.cleanup();
        }, 60000);
        
        // 每5分钟输出统计信息
        this.statsTimer = setInterval(() => {
            const stats = this.getServerStats();
            this.logger.info('服务器状态', {
                uptime: Math.round(stats.uptime / 1000) + 's',
                currentConnections: stats.currentConnections,
                messagesProcessed: stats.messagesProcessed,
                errorsOccurred: stats.errorsOccurred,
                totalPlugins: stats.plugins.totalPlugins,
                activeTasks: stats.tasks.executingTasks,
                activeTransfers: stats.fileTransfers.activeTransfers,
                memoryUsage: Math.round(stats.memoryUsage.heapUsed / 1024 / 1024) + 'MB'
            });
        }, 300000);
    }

    stopPeriodicTasks() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        if (this.statsTimer) {
            clearInterval(this.statsTimer);
            this.statsTimer = null;
        }
    }
}

// 创建服务器实例
const server = new DistributedPluginServer();

// 处理进程信号
process.on('SIGINT', async () => {
    console.log('\n收到SIGINT信号，正在关闭服务器...');
    try {
        await server.stop();
        process.exit(0);
    } catch (error) {
        console.error('关闭服务器时发生错误:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\n收到SIGTERM信号，正在关闭服务器...');
    try {
        await server.stop();
        process.exit(0);
    } catch (error) {
        console.error('关闭服务器时发生错误:', error);
        process.exit(1);
    }
});

process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    server.stop().finally(() => {
        process.exit(1);
    });
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
    server.stop().finally(() => {
        process.exit(1);
    });
});

// 启动服务器
server.start().catch((error) => {
    console.error('服务器启动失败:', error);
    process.exit(1);
});

module.exports = DistributedPluginServer;