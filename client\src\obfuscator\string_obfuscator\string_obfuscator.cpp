#include "string_obfuscator.h"
#include <algorithm>
#include <random>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <windows.h>
#include <tlhelp32.h>
#endif

namespace Obfuscator {

    // RuntimeStringObfuscator implementation
    std::string RuntimeStringObfuscator::Encrypt(const std::string& input, uint8_t key) {
        if (key == 0) {
            key = GenerateKey(input);
        }
        
        std::string result;
        result.reserve(input.length() + 1);
        
        // Prepend key to the encrypted string
        result += static_cast<char>(key);
        
        for (char c : input) {
            result += static_cast<char>(c ^ key);
        }
        
        return result;
    }

    std::string RuntimeStringObfuscator::Decrypt(const std::string& encrypted, uint8_t key) {
        if (encrypted.empty()) {
            return "";
        }
        
        if (key == 0) {
            // Extract key from first byte
            key = static_cast<uint8_t>(encrypted[0]);
        }
        
        std::string result;
        result.reserve(encrypted.length() - 1);
        
        for (size_t i = 1; i < encrypted.length(); ++i) {
            result += static_cast<char>(encrypted[i] ^ key);
        }
        
        return result;
    }

    std::string RuntimeStringObfuscator::EncryptToBase64(const std::string& input, uint8_t key) {
        std::string encrypted = Encrypt(input, key);
        return Base64Encode(encrypted);
    }

    std::string RuntimeStringObfuscator::DecryptFromBase64(const std::string& base64_encrypted, uint8_t key) {
        std::string encrypted = Base64Decode(base64_encrypted);
        return Decrypt(encrypted, key);
    }

    std::string RuntimeStringObfuscator::AdvancedEncrypt(const std::string& input) {
        if (input.empty()) {
            return "";
        }
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1, 255);
        
        // Generate multiple keys
        uint8_t key1 = dis(gen);
        uint8_t key2 = dis(gen);
        uint8_t key3 = dis(gen);
        
        std::string result;
        result.reserve(input.length() + 4);
        
        // Prepend keys
        result += static_cast<char>(key1);
        result += static_cast<char>(key2);
        result += static_cast<char>(key3);
        result += static_cast<char>(input.length() & 0xFF);
        
        // Multi-layer encryption
        for (size_t i = 0; i < input.length(); ++i) {
            char c = input[i];
            c ^= key1;
            c = ((c << 1) | (c >> 7)) & 0xFF; // Rotate left
            c ^= key2;
            c ^= static_cast<char>(i & 0xFF);
            c ^= key3;
            result += c;
        }
        
        return result;
    }

    std::string RuntimeStringObfuscator::AdvancedDecrypt(const std::string& encrypted) {
        if (encrypted.length() < 4) {
            return "";
        }
        
        uint8_t key1 = static_cast<uint8_t>(encrypted[0]);
        uint8_t key2 = static_cast<uint8_t>(encrypted[1]);
        uint8_t key3 = static_cast<uint8_t>(encrypted[2]);
        uint8_t length = static_cast<uint8_t>(encrypted[3]);
        
        std::string result;
        result.reserve(length);
        
        // Multi-layer decryption (reverse order)
        for (size_t i = 4; i < encrypted.length() && result.length() < length; ++i) {
            char c = encrypted[i];
            c ^= key3;
            c ^= static_cast<char>((i - 4) & 0xFF);
            c ^= key2;
            c = ((c >> 1) | (c << 7)) & 0xFF; // Rotate right
            c ^= key1;
            result += c;
        }
        
        return result;
    }

    uint8_t RuntimeStringObfuscator::GenerateKey(const std::string& input) {
        uint8_t key = 0x5A;
        for (char c : input) {
            key ^= static_cast<uint8_t>(c);
            key = ((key << 1) | (key >> 7)) & 0xFF;
        }
        return key ? key : 0x5A;
    }

    std::string RuntimeStringObfuscator::Base64Encode(const std::string& input) {
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int val = 0, valb = -6;
        
        for (unsigned char c : input) {
            val = (val << 8) + c;
            valb += 8;
            while (valb >= 0) {
                result.push_back(chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        
        if (valb > -6) {
            result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
        }
        
        while (result.size() % 4) {
            result.push_back('=');
        }
        
        return result;
    }

    std::string RuntimeStringObfuscator::Base64Decode(const std::string& input) {
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int val = 0, valb = -8;
        
        for (char c : input) {
            if (c == '=') break;
            
            size_t pos = chars.find(c);
            if (pos == std::string::npos) continue;
            
            val = (val << 6) + pos;
            valb += 6;
            
            if (valb >= 0) {
                result.push_back((val >> valb) & 0xFF);
                valb -= 8;
            }
        }
        
        return result;
    }

    // FunctionNameObfuscator implementation
    std::string FunctionNameObfuscator::ObfuscateName(const std::string& original_name) {
        if (original_name.empty()) {
            return "";
        }
        
        // Simple ROT13-style obfuscation with offset
        std::string result;
        result.reserve(original_name.length());
        
        for (size_t i = 0; i < original_name.length(); ++i) {
            char c = original_name[i];
            int offset = (i % 26) + 1;
            
            if (std::isalpha(c)) {
                if (std::islower(c)) {
                    c = ((c - 'a' + offset) % 26) + 'a';
                } else {
                    c = ((c - 'A' + offset) % 26) + 'A';
                }
            } else if (std::isdigit(c)) {
                c = ((c - '0' + offset) % 10) + '0';
            }
            
            result += c;
        }
        
        return result;
    }

    std::string FunctionNameObfuscator::DeobfuscateName(const std::string& obfuscated_name) {
        if (obfuscated_name.empty()) {
            return "";
        }
        
        // Reverse the obfuscation
        std::string result;
        result.reserve(obfuscated_name.length());
        
        for (size_t i = 0; i < obfuscated_name.length(); ++i) {
            char c = obfuscated_name[i];
            int offset = (i % 26) + 1;
            
            if (std::isalpha(c)) {
                if (std::islower(c)) {
                    c = ((c - 'a' - offset + 26) % 26) + 'a';
                } else {
                    c = ((c - 'A' - offset + 26) % 26) + 'A';
                }
            } else if (std::isdigit(c)) {
                c = ((c - '0' - offset + 10) % 10) + '0';
            }
            
            result += c;
        }
        
        return result;
    }

    std::string FunctionNameObfuscator::HashObfuscate(const std::string& name) {
        uint32_t hash = SimpleHash(name);
        
        std::stringstream ss;
        ss << "func_" << std::hex << hash;
        return ss.str();
    }

    bool FunctionNameObfuscator::VerifyHashObfuscated(const std::string& name, const std::string& hash) {
        return HashObfuscate(name) == hash;
    }

    uint32_t FunctionNameObfuscator::SimpleHash(const std::string& input) {
        uint32_t hash = 0x811C9DC5; // FNV offset basis
        const uint32_t prime = 0x01000193; // FNV prime
        
        for (char c : input) {
            hash ^= static_cast<uint32_t>(c);
            hash *= prime;
        }
        
        return hash;
    }

    std::string FunctionNameObfuscator::RotateString(const std::string& input, int rotation) {
        std::string result = input;
        
        if (!result.empty() && rotation != 0) {
            size_t len = result.length();
            rotation = ((rotation % static_cast<int>(len)) + len) % len;
            
            std::rotate(result.begin(), result.begin() + rotation, result.end());
        }
        
        return result;
    }

    // AntiDebugStrings implementation
    bool AntiDebugStrings::IsDebuggerPresent() {
#ifdef _WIN32
        return ::IsDebuggerPresent() != FALSE;
#else
        return false;
#endif
    }

    bool AntiDebugStrings::CheckDebuggerProcesses() {
#ifdef _WIN32
        auto debugger_names = GetObfuscatedDebuggerNames();
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) {
            return false;
        }
        
        PROCESSENTRY32 pe;
        pe.dwSize = sizeof(PROCESSENTRY32);
        
        bool found = false;
        if (Process32First(snapshot, &pe)) {
            do {
                std::string process_name = pe.szExeFile;
                std::transform(process_name.begin(), process_name.end(), process_name.begin(), ::tolower);
                
                for (const auto& debugger : debugger_names) {
                    if (process_name.find(debugger) != std::string::npos) {
                        found = true;
                        break;
                    }
                }
                
                if (found) break;
                
            } while (Process32Next(snapshot, &pe));
        }
        
        CloseHandle(snapshot);
        return found;
#else
        return false;
#endif
    }

    std::vector<std::string> AntiDebugStrings::GetDebuggerProcessNames() {
        return GetObfuscatedDebuggerNames();
    }

    std::vector<std::string> AntiDebugStrings::GetObfuscatedDebuggerNames() {
        // These are obfuscated debugger process names
        std::vector<std::string> obfuscated_names = {
            RuntimeStringObfuscator::Decrypt("\x1Foldbg", 0),  // "ollydbg"
            RuntimeStringObfuscator::Decrypt("\x15ida", 0),     // "ida"
            RuntimeStringObfuscator::Decrypt("\x05x64dbg", 0),  // "x64dbg"
            RuntimeStringObfuscator::Decrypt("\x0bwindbg", 0),  // "windbg"
            RuntimeStringObfuscator::Decrypt("\x13cheat", 0),   // "cheat"
            RuntimeStringObfuscator::Decrypt("\x0cprocess", 0), // "process"
        };
        
        // Decrypt the names
        std::vector<std::string> names;
        for (const auto& obfuscated : obfuscated_names) {
            try {
                std::string decrypted = RuntimeStringObfuscator::Decrypt(obfuscated);
                if (!decrypted.empty()) {
                    names.push_back(decrypted);
                }
            } catch (...) {
                // Ignore decryption errors
            }
        }
        
        return names;
    }

    // Security namespace implementation
    namespace Security {
        std::string EncryptString(const std::string& input, Level level) {
            switch (level) {
                case Level::NONE:
                    return input;
                case Level::BASIC:
                    return BasicEncrypt(input);
                case Level::MEDIUM:
                    return MediumEncrypt(input);
                case Level::HIGH:
                    return HighEncrypt(input);
                case Level::MAXIMUM:
                    return MaximumEncrypt(input);
                default:
                    return MediumEncrypt(input);
            }
        }

        std::string DecryptString(const std::string& encrypted, Level level) {
            switch (level) {
                case Level::NONE:
                    return encrypted;
                case Level::BASIC:
                    return BasicDecrypt(encrypted);
                case Level::MEDIUM:
                    return MediumDecrypt(encrypted);
                case Level::HIGH:
                    return HighDecrypt(encrypted);
                case Level::MAXIMUM:
                    return MaximumDecrypt(encrypted);
                default:
                    return MediumDecrypt(encrypted);
            }
        }

        std::string BasicEncrypt(const std::string& input) {
            return RuntimeStringObfuscator::Encrypt(input);
        }

        std::string MediumEncrypt(const std::string& input) {
            std::string encrypted = RuntimeStringObfuscator::Encrypt(input);
            return RuntimeStringObfuscator::Base64Encode(encrypted);
        }

        std::string HighEncrypt(const std::string& input) {
            std::string encrypted = RuntimeStringObfuscator::AdvancedEncrypt(input);
            return RuntimeStringObfuscator::Base64Encode(encrypted);
        }

        std::string MaximumEncrypt(const std::string& input) {
            // Multi-layer encryption
            std::string layer1 = RuntimeStringObfuscator::Encrypt(input, 0xAA);
            std::string layer2 = RuntimeStringObfuscator::AdvancedEncrypt(layer1);
            std::string layer3 = RuntimeStringObfuscator::Encrypt(layer2, 0x55);
            return RuntimeStringObfuscator::Base64Encode(layer3);
        }

        std::string BasicDecrypt(const std::string& encrypted) {
            return RuntimeStringObfuscator::Decrypt(encrypted);
        }

        std::string MediumDecrypt(const std::string& encrypted) {
            std::string decoded = RuntimeStringObfuscator::Base64Decode(encrypted);
            return RuntimeStringObfuscator::Decrypt(decoded);
        }

        std::string HighDecrypt(const std::string& encrypted) {
            std::string decoded = RuntimeStringObfuscator::Base64Decode(encrypted);
            return RuntimeStringObfuscator::AdvancedDecrypt(decoded);
        }

        std::string MaximumDecrypt(const std::string& encrypted) {
            // Multi-layer decryption (reverse order)
            std::string decoded = RuntimeStringObfuscator::Base64Decode(encrypted);
            std::string layer1 = RuntimeStringObfuscator::Decrypt(decoded, 0x55);
            std::string layer2 = RuntimeStringObfuscator::AdvancedDecrypt(layer1);
            return RuntimeStringObfuscator::Decrypt(layer2, 0xAA);
        }
    }

} // namespace Obfuscator