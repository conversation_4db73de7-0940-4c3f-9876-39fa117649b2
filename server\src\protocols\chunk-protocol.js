const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

// 分块传输配置
const CHUNK_CONFIG = {
    CHUNK_SIZE: 64 * 1024, // 64KB
    MAX_CONCURRENT_CHUNKS: 10,
    MAX_RETRIES: 3,
    TIMEOUT: 30000, // 30秒
    HASH_ALGORITHM: 'md5'
};

// 传输状态
const TRANSFER_STATUS = {
    PENDING: 'pending',
    TRANSFERRING: 'transferring',
    PAUSED: 'paused',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};

// 分块状态
const CHUNK_STATUS = {
    PENDING: 'pending',
    TRANSFERRING: 'transferring',
    COMPLETED: 'completed',
    FAILED: 'failed',
    TIMEOUT: 'timeout'
};

class ChunkProtocol extends EventEmitter {
    constructor(options = {}) {
        super();
        this.chunkSize = options.chunkSize || CHUNK_CONFIG.CHUNK_SIZE;
        this.maxConcurrentChunks = options.maxConcurrentChunks || CHUNK_CONFIG.MAX_CONCURRENT_CHUNKS;
        this.maxRetries = options.maxRetries || CHUNK_CONFIG.MAX_RETRIES;
        this.timeout = options.timeout || CHUNK_CONFIG.TIMEOUT;
        
        // 活跃传输会话
        this.transfers = new Map();
        
        // 分块缓存
        this.chunkCache = new Map();
        
        // 传输统计
        this.stats = {
            totalTransfers: 0,
            activeTransfers: 0,
            completedTransfers: 0,
            failedTransfers: 0,
            totalBytesTransferred: 0
        };
    }

    // 初始化传输会话
    initializeTransfer(transferId, data, options = {}) {
        const chunks = this.splitIntoChunks(data, transferId);
        const transfer = {
            id: transferId,
            totalSize: data.length,
            totalChunks: chunks.length,
            chunks: chunks,
            status: TRANSFER_STATUS.PENDING,
            startTime: Date.now(),
            endTime: null,
            retryCount: 0,
            completedChunks: 0,
            failedChunks: 0,
            clientId: options.clientId,
            transferType: options.transferType || 'upload',
            metadata: options.metadata || {},
            checksum: this.calculateChecksum(data)
        };

        this.transfers.set(transferId, transfer);
        this.stats.totalTransfers++;
        this.stats.activeTransfers++;

        console.log(`初始化传输会话: ${transferId}, 大小: ${data.length}字节, 分块数: ${chunks.length}`);
        this.emit('transferInitialized', transfer);
        
        return transfer;
    }

    // 将数据分割成分块
    splitIntoChunks(data, transferId) {
        const chunks = [];
        const totalChunks = Math.ceil(data.length / this.chunkSize);
        
        for (let i = 0; i < totalChunks; i++) {
            const start = i * this.chunkSize;
            const end = Math.min(start + this.chunkSize, data.length);
            const chunkData = data.slice(start, end);
            
            const chunk = {
                id: `${transferId}_chunk_${i}`,
                transferId: transferId,
                sequence: i,
                totalChunks: totalChunks,
                size: chunkData.length,
                data: chunkData,
                checksum: this.calculateChecksum(chunkData),
                status: CHUNK_STATUS.PENDING,
                retryCount: 0,
                createdAt: Date.now(),
                sentAt: null,
                acknowledgedAt: null
            };
            
            chunks.push(chunk);
            this.chunkCache.set(chunk.id, chunk);
        }
        
        return chunks;
    }

    // 开始传输
    startTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            throw new Error(`传输会话不存在: ${transferId}`);
        }

        if (transfer.status !== TRANSFER_STATUS.PENDING && transfer.status !== TRANSFER_STATUS.PAUSED) {
            throw new Error(`传输会话状态无效: ${transfer.status}`);
        }

        transfer.status = TRANSFER_STATUS.TRANSFERRING;
        transfer.startTime = Date.now();

        console.log(`开始传输: ${transferId}`);
        this.emit('transferStarted', transfer);

        // 开始发送分块
        this.sendNextChunks(transferId);
        
        return transfer;
    }

    // 发送下一批分块
    sendNextChunks(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer || transfer.status !== TRANSFER_STATUS.TRANSFERRING) {
            return;
        }

        // 获取待发送的分块
        const pendingChunks = transfer.chunks.filter(chunk => 
            chunk.status === CHUNK_STATUS.PENDING
        ).slice(0, this.maxConcurrentChunks);

        if (pendingChunks.length === 0) {
            // 检查是否所有分块都已完成
            this.checkTransferCompletion(transferId);
            return;
        }

        // 发送分块
        for (const chunk of pendingChunks) {
            this.sendChunk(chunk);
        }
    }

    // 发送单个分块
    sendChunk(chunk) {
        chunk.status = CHUNK_STATUS.TRANSFERRING;
        chunk.sentAt = Date.now();

        const chunkMessage = {
            type: 'chunk_data',
            id: uuidv4(),
            timestamp: Date.now(),
            data: {
                chunkId: chunk.id,
                transferId: chunk.transferId,
                sequence: chunk.sequence,
                totalChunks: chunk.totalChunks,
                chunkSize: chunk.size,
                data: chunk.data.toString('base64'),
                checksum: chunk.checksum
            }
        };

        // 设置分块超时
        setTimeout(() => {
            if (chunk.status === CHUNK_STATUS.TRANSFERRING) {
                this.handleChunkTimeout(chunk);
            }
        }, this.timeout);

        console.log(`发送分块: ${chunk.id} (${chunk.sequence + 1}/${chunk.totalChunks})`);
        this.emit('chunkSent', chunk, chunkMessage);
    }

    // 处理分块确认
    handleChunkAck(ackMessage) {
        const { chunkId, success, reason } = ackMessage.data;
        const chunk = this.chunkCache.get(chunkId);
        
        if (!chunk) {
            console.warn(`收到未知分块的确认: ${chunkId}`);
            return;
        }

        const transfer = this.transfers.get(chunk.transferId);
        if (!transfer) {
            console.warn(`传输会话不存在: ${chunk.transferId}`);
            return;
        }

        chunk.acknowledgedAt = Date.now();

        if (success) {
            chunk.status = CHUNK_STATUS.COMPLETED;
            transfer.completedChunks++;
            
            console.log(`分块确认成功: ${chunkId} (${transfer.completedChunks}/${transfer.totalChunks})`);
            this.emit('chunkCompleted', chunk);
            
            // 继续发送下一批分块
            this.sendNextChunks(chunk.transferId);
        } else {
            chunk.status = CHUNK_STATUS.FAILED;
            chunk.retryCount++;
            transfer.failedChunks++;
            
            console.warn(`分块确认失败: ${chunkId}, 原因: ${reason}`);
            
            // 重试机制
            if (chunk.retryCount <= this.maxRetries) {
                console.log(`重试分块: ${chunkId} (${chunk.retryCount}/${this.maxRetries})`);
                chunk.status = CHUNK_STATUS.PENDING;
                transfer.failedChunks--;
                
                setTimeout(() => {
                    this.sendChunk(chunk);
                }, 1000 * chunk.retryCount); // 指数退避
            } else {
                console.error(`分块重试次数超限: ${chunkId}`);
                this.failTransfer(chunk.transferId, `分块传输失败: ${chunkId}`);
            }
            
            this.emit('chunkFailed', chunk, reason);
        }
    }

    // 处理分块超时
    handleChunkTimeout(chunk) {
        chunk.status = CHUNK_STATUS.TIMEOUT;
        chunk.retryCount++;
        
        const transfer = this.transfers.get(chunk.transferId);
        if (transfer) {
            transfer.failedChunks++;
        }

        console.warn(`分块传输超时: ${chunk.id}`);
        
        // 重试机制
        if (chunk.retryCount <= this.maxRetries) {
            console.log(`重试超时分块: ${chunk.id} (${chunk.retryCount}/${this.maxRetries})`);
            chunk.status = CHUNK_STATUS.PENDING;
            if (transfer) {
                transfer.failedChunks--;
            }
            
            setTimeout(() => {
                this.sendChunk(chunk);
            }, 2000 * chunk.retryCount);
        } else {
            console.error(`分块超时重试次数超限: ${chunk.id}`);
            this.failTransfer(chunk.transferId, `分块传输超时: ${chunk.id}`);
        }
        
        this.emit('chunkTimeout', chunk);
    }

    // 检查传输完成状态
    checkTransferCompletion(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return;
        }

        const completedChunks = transfer.chunks.filter(chunk => 
            chunk.status === CHUNK_STATUS.COMPLETED
        ).length;

        const failedChunks = transfer.chunks.filter(chunk => 
            chunk.status === CHUNK_STATUS.FAILED && chunk.retryCount > this.maxRetries
        ).length;

        if (completedChunks === transfer.totalChunks) {
            this.completeTransfer(transferId);
        } else if (failedChunks > 0) {
            this.failTransfer(transferId, '存在无法重试的失败分块');
        }
    }

    // 完成传输
    completeTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return;
        }

        transfer.status = TRANSFER_STATUS.COMPLETED;
        transfer.endTime = Date.now();
        
        // 验证数据完整性
        const reconstructedData = this.reconstructData(transfer);
        const calculatedChecksum = this.calculateChecksum(reconstructedData);
        
        if (calculatedChecksum !== transfer.checksum) {
            console.error(`传输数据校验失败: ${transferId}`);
            this.failTransfer(transferId, '数据校验失败');
            return;
        }

        this.stats.activeTransfers--;
        this.stats.completedTransfers++;
        this.stats.totalBytesTransferred += transfer.totalSize;

        const duration = transfer.endTime - transfer.startTime;
        const speed = (transfer.totalSize / duration * 1000).toFixed(2); // bytes/sec

        console.log(`传输完成: ${transferId}, 耗时: ${duration}ms, 速度: ${speed} B/s`);
        
        // 清理分块缓存
        for (const chunk of transfer.chunks) {
            this.chunkCache.delete(chunk.id);
        }

        this.emit('transferCompleted', transfer, reconstructedData);
    }

    // 失败传输
    failTransfer(transferId, reason) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return;
        }

        transfer.status = TRANSFER_STATUS.FAILED;
        transfer.endTime = Date.now();
        transfer.failureReason = reason;

        this.stats.activeTransfers--;
        this.stats.failedTransfers++;

        console.error(`传输失败: ${transferId}, 原因: ${reason}`);
        
        // 清理分块缓存
        for (const chunk of transfer.chunks) {
            this.chunkCache.delete(chunk.id);
        }

        this.emit('transferFailed', transfer, reason);
    }

    // 暂停传输
    pauseTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer || transfer.status !== TRANSFER_STATUS.TRANSFERRING) {
            return false;
        }

        transfer.status = TRANSFER_STATUS.PAUSED;
        console.log(`暂停传输: ${transferId}`);
        this.emit('transferPaused', transfer);
        
        return true;
    }

    // 恢复传输
    resumeTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer || transfer.status !== TRANSFER_STATUS.PAUSED) {
            return false;
        }

        transfer.status = TRANSFER_STATUS.TRANSFERRING;
        console.log(`恢复传输: ${transferId}`);
        this.emit('transferResumed', transfer);
        
        // 继续发送分块
        this.sendNextChunks(transferId);
        
        return true;
    }

    // 取消传输
    cancelTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return false;
        }

        transfer.status = TRANSFER_STATUS.CANCELLED;
        transfer.endTime = Date.now();

        this.stats.activeTransfers--;

        console.log(`取消传输: ${transferId}`);
        
        // 清理分块缓存
        for (const chunk of transfer.chunks) {
            this.chunkCache.delete(chunk.id);
        }

        this.emit('transferCancelled', transfer);
        
        return true;
    }

    // 重构数据
    reconstructData(transfer) {
        const sortedChunks = transfer.chunks.sort((a, b) => a.sequence - b.sequence);
        const buffers = sortedChunks.map(chunk => chunk.data);
        return Buffer.concat(buffers);
    }

    // 计算校验和
    calculateChecksum(data) {
        return crypto.createHash(CHUNK_CONFIG.HASH_ALGORITHM).update(data).digest('hex');
    }

    // 获取传输信息
    getTransfer(transferId) {
        const transfer = this.transfers.get(transferId);
        if (!transfer) {
            return null;
        }

        return {
            id: transfer.id,
            totalSize: transfer.totalSize,
            totalChunks: transfer.totalChunks,
            completedChunks: transfer.completedChunks,
            failedChunks: transfer.failedChunks,
            status: transfer.status,
            startTime: transfer.startTime,
            endTime: transfer.endTime,
            progress: (transfer.completedChunks / transfer.totalChunks * 100).toFixed(2),
            clientId: transfer.clientId,
            transferType: transfer.transferType,
            metadata: transfer.metadata
        };
    }

    // 获取所有传输
    getAllTransfers() {
        return Array.from(this.transfers.keys()).map(transferId => 
            this.getTransfer(transferId)
        );
    }

    // 获取活跃传输
    getActiveTransfers() {
        return this.getAllTransfers().filter(transfer => 
            transfer.status === TRANSFER_STATUS.TRANSFERRING || 
            transfer.status === TRANSFER_STATUS.PAUSED
        );
    }

    // 获取传输统计
    getStats() {
        return {
            ...this.stats,
            averageTransferSize: this.stats.totalTransfers > 0 ? 
                (this.stats.totalBytesTransferred / this.stats.completedTransfers).toFixed(2) : 0,
            successRate: this.stats.totalTransfers > 0 ? 
                (this.stats.completedTransfers / this.stats.totalTransfers * 100).toFixed(2) : 0
        };
    }

    // 清理过期传输
    cleanupExpiredTransfers(maxAge = 24 * 60 * 60 * 1000) { // 24小时
        const now = Date.now();
        let cleanedCount = 0;

        for (const [transferId, transfer] of this.transfers) {
            const age = now - transfer.startTime;
            
            if (age > maxAge && (
                transfer.status === TRANSFER_STATUS.COMPLETED ||
                transfer.status === TRANSFER_STATUS.FAILED ||
                transfer.status === TRANSFER_STATUS.CANCELLED
            )) {
                // 清理分块缓存
                for (const chunk of transfer.chunks) {
                    this.chunkCache.delete(chunk.id);
                }
                
                this.transfers.delete(transferId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期传输会话`);
        }

        return cleanedCount;
    }

    // 获取配置
    getConfig() {
        return {
            chunkSize: this.chunkSize,
            maxConcurrentChunks: this.maxConcurrentChunks,
            maxRetries: this.maxRetries,
            timeout: this.timeout
        };
    }
}

module.exports = {
    ChunkProtocol,
    CHUNK_CONFIG,
    TRANSFER_STATUS,
    CHUNK_STATUS
};