#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <iostream>
#include <fstream>
#include <sstream>
#include <cstring>
#include <ctime>
#include <iomanip>

#ifdef _WIN32
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <psapi.h>
#include <tlhelp32.h>
#undef ERROR  // Undefine Windows ERROR macro
#else
#include <dlfcn.h>
#include <unistd.h>
#include <sys/types.h>
#endif

// Forward declarations
namespace Network {
    class WebSocketClient;
    class ProtocolHandler;
}

namespace Crypto {
    class XORCrypto;
}

namespace Loader {
    class PELoader;
    class MemoryLoader;
}

namespace Plugins {
    class PluginManager;
    class PluginExecutor;
}

namespace Core {
    class Client;
    class TaskManager;
}

// Common types
using ByteArray = std::vector<uint8_t>;
using StringMap = std::map<std::string, std::string>;
using TaskCallback = std::function<void(bool success, const std::string& result)>;

// Plugin interface
class IPlugin {
public:
    virtual ~IPlugin() = default;
    virtual bool Initialize() = 0;
    virtual bool Execute(const StringMap& params, std::string& result) = 0;
    virtual void Cleanup() = 0;
    virtual std::string GetName() const = 0;
    virtual std::string GetVersion() const = 0;
};

// Task structure
struct Task {
    std::string id;
    std::string type;
    std::string plugin_name;
    StringMap parameters;
    TaskCallback callback;
    std::chrono::steady_clock::time_point created_at;
    uint32_t priority = 0;
    uint32_t retry_count = 0;
    uint32_t max_retries = 3;
};

// Message structure for protocol
struct Message {
    std::string type;
    std::string id;
    std::string target;
    ByteArray payload;
    StringMap metadata;
    bool encrypted = true;
    
    Message() = default;
    Message(const std::string& msg_type, const std::string& msg_id = "")
        : type(msg_type), id(msg_id) {}
};

// Logging macros
#define LOG_DEBUG(msg) Logger::Instance().Log(Logger::Level::DEBUG, msg, __FILE__, __LINE__)
#define LOG_INFO(msg) Logger::Instance().Log(Logger::Level::INFO, msg, __FILE__, __LINE__)
#define LOG_WARNING(msg) Logger::Instance().Log(Logger::Level::WARNING, msg, __FILE__, __LINE__)
#define LOG_ERROR(msg) Logger::Instance().Log(Logger::Level::ERROR, msg, __FILE__, __LINE__)

// Simple logger class
class Logger {
public:
    enum class Level { DEBUG, INFO, WARNING, ERROR };
    
    static Logger& Instance() {
        static Logger instance;
        return instance;
    }
    
    void Log(Level level, const std::string& message, const char* file = nullptr, int line = 0);
    void SetLevel(Level level) { current_level_ = level; }
    void SetOutputFile(const std::string& filename);
    
private:
    Logger() = default;
    Level current_level_ = Level::INFO;
    std::ofstream log_file_;
    std::mutex log_mutex_;
    
    std::string LevelToString(Level level);
    std::string GetTimestamp();
};