#include "pe_loader.h"

#ifdef _WIN32

namespace Loader {

    PELoader::PELoader() : security_level_(1) {
        LOG_INFO("PE Loader initialized");
    }

    PELoader::~PELoader() {
        CleanupAll();
    }

    HMODULE PELoader::LoadFromMemory(const ByteArray& pe_data, const std::string& module_name) {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        if (pe_data.empty()) {
            LOG_ERROR("Empty PE data provided");
            return nullptr;
        }
        
        if (!ValidatePE(pe_data)) {
            LOG_ERROR("Invalid PE file format");
            return nullptr;
        }
        
        if (security_level_ > 0 && !PerformSecurityChecks(pe_data)) {
            LOG_ERROR("Security checks failed for PE file");
            return nullptr;
        }
        
        std::string final_module_name = module_name.empty() ? GenerateModuleName() : module_name;
        
        if (IsModuleLoaded(final_module_name)) {
            LOG_WARNING("Module already loaded: " + final_module_name);
            return GetModuleHandle(final_module_name);
        }
        
        HMODULE module_handle = LoadPEFromMemory(pe_data, final_module_name);
        if (!module_handle) {
            LOG_ERROR("Failed to load PE from memory: " + final_module_name);
            return nullptr;
        }
        
        // Store module information
        LoadedModule module_info;
        module_info.base_address = module_handle;
        module_info.size = GetPEImageSize(pe_data);
        module_info.name = final_module_name;
        module_info.exports = GetExports(pe_data);
        module_info.is_memory_loaded = true;
        module_info.load_time = std::chrono::steady_clock::now();
        
        loaded_modules_[final_module_name] = module_info;
        handle_to_name_[module_handle] = final_module_name;
        
        LogPEInfo(pe_data, final_module_name);
        LOG_INFO("Successfully loaded PE module: " + final_module_name);
        
        return module_handle;
    }

    HMODULE PELoader::LoadFromFile(const std::string& file_path) {
        try {
            std::ifstream file(file_path, std::ios::binary | std::ios::ate);
            if (!file) {
                LOG_ERROR("Cannot open file: " + file_path);
                return nullptr;
            }
            
            size_t file_size = file.tellg();
            file.seekg(0, std::ios::beg);
            
            ByteArray pe_data(file_size);
            file.read(reinterpret_cast<char*>(pe_data.data()), file_size);
            
            std::string module_name = file_path.substr(file_path.find_last_of("/\\") + 1);
            return LoadFromMemory(pe_data, module_name);
            
        } catch (const std::exception& e) {
            LOG_ERROR("Exception loading file " + file_path + ": " + e.what());
            return nullptr;
        }
    }

    bool PELoader::UnloadModule(HMODULE module_handle) {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        auto name_it = handle_to_name_.find(module_handle);
        if (name_it == handle_to_name_.end()) {
            LOG_ERROR("Module handle not found");
            return false;
        }
        
        return UnloadModule(name_it->second);
    }

    bool PELoader::UnloadModule(const std::string& module_name) {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        auto it = loaded_modules_.find(module_name);
        if (it == loaded_modules_.end()) {
            LOG_ERROR("Module not found: " + module_name);
            return false;
        }
        
        LoadedModule& module_info = it->second;
        
        try {
            if (module_info.is_memory_loaded && module_info.base_address) {
                // Call DLL_PROCESS_DETACH if this is a DLL
                DLLEntryProc dll_entry = reinterpret_cast<DLLEntryProc>(
                    GetProcAddress(module_info.base_address, "DllMain"));
                
                if (dll_entry) {
                    dll_entry(module_info.base_address, DLL_PROCESS_DETACH, nullptr);
                }
                
                // Free the allocated memory
                FreeAllocatedMemory(module_info.base_address, module_info.size);
            }
            
            // Remove from tracking
            handle_to_name_.erase(module_info.base_address);
            loaded_modules_.erase(it);
            
            LOG_INFO("Successfully unloaded module: " + module_name);
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("Exception unloading module " + module_name + ": " + e.what());
            return false;
        }
    }

    bool PELoader::IsModuleLoaded(const std::string& module_name) const {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        return loaded_modules_.find(module_name) != loaded_modules_.end();
    }

    HMODULE PELoader::GetModuleHandle(const std::string& module_name) const {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        auto it = loaded_modules_.find(module_name);
        if (it != loaded_modules_.end()) {
            return it->second.base_address;
        }
        
        return nullptr;
    }

    std::vector<std::string> PELoader::GetLoadedModules() const {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        std::vector<std::string> modules;
        for (const auto& pair : loaded_modules_) {
            modules.push_back(pair.first);
        }
        
        return modules;
    }

    LoadedModule* PELoader::GetModuleInfo(const std::string& module_name) {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        auto it = loaded_modules_.find(module_name);
        if (it != loaded_modules_.end()) {
            return &it->second;
        }
        
        return nullptr;
    }

    LoadedModule* PELoader::GetModuleInfo(HMODULE module_handle) {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        auto name_it = handle_to_name_.find(module_handle);
        if (name_it != handle_to_name_.end()) {
            return GetModuleInfo(name_it->second);
        }
        
        return nullptr;
    }

    FARPROC PELoader::GetProcAddress(HMODULE module_handle, const std::string& function_name) {
        if (!module_handle) {
            return nullptr;
        }
        
        return ::GetProcAddress(module_handle, function_name.c_str());
    }

    FARPROC PELoader::GetProcAddress(const std::string& module_name, const std::string& function_name) {
        HMODULE module_handle = GetModuleHandle(module_name);
        return GetProcAddress(module_handle, function_name);
    }

    bool PELoader::ValidatePE(const ByteArray& pe_data) const {
        if (pe_data.size() < sizeof(IMAGE_DOS_HEADER)) {
            return false;
        }
        
        PIMAGE_DOS_HEADER dos_header = GetDosHeader(pe_data);
        if (!dos_header || dos_header->e_magic != IMAGE_DOS_SIGNATURE) {
            return false;
        }
        
        if (static_cast<size_t>(dos_header->e_lfanew) + sizeof(IMAGE_NT_HEADERS) > pe_data.size()) {
            return false;
        }
        
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        if (!nt_headers || nt_headers->Signature != IMAGE_NT_SIGNATURE) {
            return false;
        }
        
        return true;
    }

    bool PELoader::Is64Bit(const ByteArray& pe_data) const {
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        if (!nt_headers) {
            return false;
        }
        
        return nt_headers->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC;
    }

    std::vector<std::string> PELoader::GetExports(const ByteArray& pe_data) const {
        std::vector<std::string> exports;
        
        // This is a simplified implementation
        // In a real scenario, you would parse the export table
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        if (!nt_headers) {
            return exports;
        }
        
        // Export parsing would go here
        // For now, return empty vector
        
        return exports;
    }

    std::vector<std::string> PELoader::GetImports(const ByteArray& pe_data) const {
        std::vector<std::string> imports;
        
        // This is a simplified implementation
        // In a real scenario, you would parse the import table
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        if (!nt_headers) {
            return imports;
        }
        
        // Import parsing would go here
        // For now, return empty vector
        
        return imports;
    }

    void PELoader::CleanupAll() {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        for (auto& pair : loaded_modules_) {
            try {
                LoadedModule& module_info = pair.second;
                if (module_info.is_memory_loaded && module_info.base_address) {
                    FreeAllocatedMemory(module_info.base_address, module_info.size);
                }
            } catch (const std::exception& e) {
                LOG_ERROR("Exception during cleanup: " + std::string(e.what()));
            }
        }
        
        loaded_modules_.clear();
        handle_to_name_.clear();
        
        LOG_INFO("All modules cleaned up");
    }

    size_t PELoader::GetTotalMemoryUsage() const {
        std::lock_guard<std::mutex> lock(modules_mutex_);
        
        size_t total = 0;
        for (const auto& pair : loaded_modules_) {
            total += pair.second.size;
        }
        
        return total;
    }

    // Private implementation methods

    HMODULE PELoader::LoadPEFromMemory(const ByteArray& pe_data, const std::string& module_name) {
        PIMAGE_DOS_HEADER dos_header = GetDosHeader(pe_data);
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        
        if (!dos_header || !nt_headers) {
            return nullptr;
        }
        
        // Allocate memory for the PE image
        size_t image_size = nt_headers->OptionalHeader.SizeOfImage;
        LPVOID base_address = nullptr;
        
        if (!AllocateMemoryForPE(image_size, 
                                reinterpret_cast<LPVOID>(nt_headers->OptionalHeader.ImageBase), 
                                &base_address)) {
            LOG_ERROR("Failed to allocate memory for PE image");
            return nullptr;
        }
        
        // Copy headers
        size_t headers_size = nt_headers->OptionalHeader.SizeOfHeaders;
        memcpy(base_address, pe_data.data(), headers_size);
        
        // Map sections
        if (!MapSections(pe_data, base_address, nt_headers)) {
            FreeAllocatedMemory(base_address, image_size);
            return nullptr;
        }
        
        // Perform relocations if necessary
        DWORD_PTR delta = reinterpret_cast<DWORD_PTR>(base_address) - nt_headers->OptionalHeader.ImageBase;
        if (delta != 0) {
            if (!RelocateImage(base_address, nt_headers, delta)) {
                LOG_WARNING("Failed to relocate image, may cause issues");
            }
        }
        
        // Resolve imports
        if (!ResolveImports(base_address, nt_headers)) {
            FreeAllocatedMemory(base_address, image_size);
            return nullptr;
        }
        
        // Protect sections
        if (!ProtectSections(base_address, nt_headers)) {
            LOG_WARNING("Failed to protect sections");
        }
        
        // Call DLL entry point if this is a DLL
        DWORD entry_point = nt_headers->OptionalHeader.AddressOfEntryPoint;
        if (entry_point != 0) {
            DLLEntryProc dll_entry = reinterpret_cast<DLLEntryProc>(
                reinterpret_cast<BYTE*>(base_address) + entry_point);
            
            try {
                if (!dll_entry(reinterpret_cast<HINSTANCE>(base_address), DLL_PROCESS_ATTACH, nullptr)) {
                    LOG_ERROR("DLL entry point returned FALSE");
                    FreeAllocatedMemory(base_address, image_size);
                    return nullptr;
                }
            } catch (const std::exception& e) {
                LOG_ERROR("Exception calling DLL entry point: " + std::string(e.what()));
                FreeAllocatedMemory(base_address, image_size);
                return nullptr;
            }
        }
        
        return reinterpret_cast<HMODULE>(base_address);
    }

    bool PELoader::MapSections(const ByteArray& pe_data, LPVOID base_address, PIMAGE_NT_HEADERS nt_headers) {
        PIMAGE_SECTION_HEADER section_headers = GetSectionHeaders(nt_headers);
        if (!section_headers) {
            return false;
        }
        
        for (WORD i = 0; i < nt_headers->FileHeader.NumberOfSections; ++i) {
            PIMAGE_SECTION_HEADER section = &section_headers[i];
            
            if (section->SizeOfRawData == 0) {
                continue;
            }
            
            LPVOID section_address = reinterpret_cast<BYTE*>(base_address) + section->VirtualAddress;
            
            // Copy section data
            memcpy(section_address, 
                   pe_data.data() + section->PointerToRawData, 
                   section->SizeOfRawData);
        }
        
        return true;
    }

    bool PELoader::RelocateImage(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers, DWORD_PTR delta) {
        // Simplified relocation implementation
        // In a complete implementation, you would process the relocation table
        LOG_DEBUG("Performing image relocation with delta: " + std::to_string(delta));
        return true;
    }

    bool PELoader::ResolveImports(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers) {
        // Simplified import resolution implementation
        // In a complete implementation, you would process the import table
        LOG_DEBUG("Resolving imports for loaded PE");
        return true;
    }

    bool PELoader::ProtectSections(LPVOID base_address, PIMAGE_NT_HEADERS nt_headers) {
        PIMAGE_SECTION_HEADER section_headers = GetSectionHeaders(nt_headers);
        if (!section_headers) {
            return false;
        }
        
        for (WORD i = 0; i < nt_headers->FileHeader.NumberOfSections; ++i) {
            PIMAGE_SECTION_HEADER section = &section_headers[i];
            
            LPVOID section_address = reinterpret_cast<BYTE*>(base_address) + section->VirtualAddress;
            DWORD protection = GetSectionCharacteristics(section->Characteristics);
            DWORD old_protection;
            
            if (!VirtualProtect(section_address, section->Misc.VirtualSize, protection, &old_protection)) {
                LOG_WARNING("Failed to protect section: " + std::string(reinterpret_cast<char*>(section->Name), 8));
                return false;
            }
        }
        
        return true;
    }

    // Helper functions

    PIMAGE_DOS_HEADER PELoader::GetDosHeader(const ByteArray& pe_data) const {
        if (pe_data.size() < sizeof(IMAGE_DOS_HEADER)) {
            return nullptr;
        }
        
        return reinterpret_cast<PIMAGE_DOS_HEADER>(const_cast<uint8_t*>(pe_data.data()));
    }

    PIMAGE_NT_HEADERS PELoader::GetNtHeaders(const ByteArray& pe_data) const {
        PIMAGE_DOS_HEADER dos_header = GetDosHeader(pe_data);
        if (!dos_header) {
            return nullptr;
        }
        
        if (static_cast<size_t>(dos_header->e_lfanew) + sizeof(IMAGE_NT_HEADERS) > pe_data.size()) {
            return nullptr;
        }
        
        return reinterpret_cast<PIMAGE_NT_HEADERS>(const_cast<uint8_t*>(pe_data.data()) + dos_header->e_lfanew);
    }

    PIMAGE_SECTION_HEADER PELoader::GetSectionHeaders(PIMAGE_NT_HEADERS nt_headers) const {
        if (!nt_headers) {
            return nullptr;
        }
        
        return IMAGE_FIRST_SECTION(nt_headers);
    }

    DWORD PELoader::GetSectionCharacteristics(DWORD characteristics) const {
        DWORD protection = PAGE_NOACCESS;
        
        if (characteristics & IMAGE_SCN_MEM_EXECUTE) {
            if (characteristics & IMAGE_SCN_MEM_WRITE) {
                protection = PAGE_EXECUTE_READWRITE;
            } else {
                protection = PAGE_EXECUTE_READ;
            }
        } else if (characteristics & IMAGE_SCN_MEM_WRITE) {
            protection = PAGE_READWRITE;
        } else if (characteristics & IMAGE_SCN_MEM_READ) {
            protection = PAGE_READONLY;
        }
        
        return protection;
    }

    bool PELoader::AllocateMemoryForPE(size_t size, LPVOID preferred_base, LPVOID* allocated_base) {
        // Try to allocate at preferred base first
        *allocated_base = VirtualAlloc(preferred_base, size, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        
        if (!*allocated_base) {
            // If that fails, allocate anywhere
            *allocated_base = VirtualAlloc(nullptr, size, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        }
        
        return *allocated_base != nullptr;
    }

    void PELoader::FreeAllocatedMemory(LPVOID base_address, size_t size) {
        if (base_address) {
            VirtualFree(base_address, 0, MEM_RELEASE);
        }
    }

    std::string PELoader::GenerateModuleName() const {
        static std::atomic<uint32_t> counter(0);
        return "memory_module_" + std::to_string(++counter);
    }

    bool PELoader::PerformSecurityChecks(const ByteArray& pe_data) const {
        // Basic security checks
        if (security_level_ >= 2) {
            return CheckDigitalSignature(pe_data);
        }
        
        if (security_level_ >= 1) {
            return ScanForMaliciousPatterns(pe_data);
        }
        
        return true;
    }

    bool PELoader::CheckDigitalSignature(const ByteArray& pe_data) const {
        // Placeholder for digital signature verification
        LOG_DEBUG("Checking digital signature");
        return true;
    }

    bool PELoader::ScanForMaliciousPatterns(const ByteArray& pe_data) const {
        // Placeholder for malicious pattern scanning
        LOG_DEBUG("Scanning for malicious patterns");
        return true;
    }

    void PELoader::LogPEInfo(const ByteArray& pe_data, const std::string& module_name) const {
        PIMAGE_NT_HEADERS nt_headers = GetNtHeaders(pe_data);
        if (nt_headers) {
            LOG_INFO("PE Info - Module: " + module_name + 
                    ", Size: " + std::to_string(nt_headers->OptionalHeader.SizeOfImage) +
                    ", Sections: " + std::to_string(nt_headers->FileHeader.NumberOfSections) +
                    ", Architecture: " + (Is64Bit(pe_data) ? "x64" : "x86"));
        }
    }

    // Utility functions

    bool IsPEFile(const ByteArray& data) {
        if (data.size() < sizeof(IMAGE_DOS_HEADER)) {
            return false;
        }
        
        PIMAGE_DOS_HEADER dos_header = reinterpret_cast<PIMAGE_DOS_HEADER>(const_cast<uint8_t*>(data.data()));
        return dos_header->e_magic == IMAGE_DOS_SIGNATURE;
    }

    size_t GetPEImageSize(const ByteArray& pe_data) {
        PELoader loader;
        if (!loader.ValidatePE(pe_data)) {
            return 0;
        }
        
        PIMAGE_NT_HEADERS nt_headers = reinterpret_cast<PIMAGE_NT_HEADERS>(
            const_cast<uint8_t*>(pe_data.data()) + 
            reinterpret_cast<PIMAGE_DOS_HEADER>(const_cast<uint8_t*>(pe_data.data()))->e_lfanew);
        
        return nt_headers->OptionalHeader.SizeOfImage;
    }

    std::string GetPEArchitecture(const ByteArray& pe_data) {
        PELoader loader;
        return loader.Is64Bit(pe_data) ? "x64" : "x86";
    }

} // namespace Loader

#endif // _WIN32