const WebSocket = require('ws');

class ProperTestClient {
    constructor(url = 'ws://localhost:8080') {
        this.url = url;
        this.ws = null;
        this.connected = false;
        this.clientId = null;
        
        // 与服务器相同的加密密钥
        this.cryptoKey = Buffer.from([
            0x4A, 0x7B, 0x9C, 0x2D, 0x8E, 0x5F, 0x31, 0xA6,
            0xB7, 0x48, 0x19, 0xCA, 0x3B, 0xDC, 0x6E, 0x4F,
            0x82, 0x53, 0x94, 0x25, 0xE6, 0x17, 0x78, 0x9A,
            0x1B, 0xFC, 0x2D, 0x8E, 0x5F, 0x60, 0x91, 0xC2
        ]);
    }

    // XOR解密函数
    decryptData(encryptedBase64) {
        try {
            const encryptedBuffer = Buffer.from(encryptedBase64, 'base64');
            const result = Buffer.alloc(encryptedBuffer.length);
            
            for (let i = 0; i < encryptedBuffer.length; i++) {
                result[i] = encryptedBuffer[i] ^ this.cryptoKey[i % this.cryptoKey.length];
            }
            
            return result.toString('utf8');
        } catch (error) {
            console.error('解密失败:', error);
            return null;
        }
    }

    connect() {
        return new Promise((resolve, reject) => {
            console.log(`正在连接到服务器: ${this.url}`);
            
            this.ws = new WebSocket(this.url);
            
            this.ws.on('open', () => {
                console.log('✅ 连接成功！');
                this.connected = true;
                resolve();
            });
            
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    
                    // 如果消息是加密的，先解密
                    if (message.encrypted && message.data) {
                        const decrypted = this.decryptData(message.data);
                        if (decrypted) {
                            try {
                                message.data = JSON.parse(decrypted);
                                console.log('📨 收到加密消息(已解密):', JSON.stringify(message, null, 2));
                            } catch (e) {
                                console.log('📨 收到加密消息(解密后非JSON):', decrypted);
                            }
                        }
                    } else {
                        console.log('📨 收到消息:', JSON.stringify(message, null, 2));
                    }
                    
                    // 处理不同类型的消息
                    switch (message.type) {
                        case 'connection_established':
                            this.clientId = message.clientId;
                            console.log(`🆔 客户端ID: ${this.clientId}`);
                            break;
                        case 'heartbeat':
                            console.log('💓 收到心跳');
                            this.sendHeartbeatResponse();
                            break;
                        case 'error':
                            console.error('❌ 服务器错误:', message.data);
                            break;
                        case 'plugin_list_response':
                            console.log('📋 插件列表:', message.data);
                            break;
                        case 'system_info_response':
                            console.log('💻 系统信息:', message.data);
                            break;
                        case 'client_list_response':
                            console.log('👥 客户端列表:', message.data);
                            break;
                    }
                } catch (error) {
                    console.error('解析消息失败:', error);
                    console.log('原始数据:', data.toString());
                }
            });
            
            this.ws.on('close', (code, reason) => {
                console.log(`🔌 连接关闭: ${code} - ${reason}`);
                this.connected = false;
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error);
                reject(error);
            });
        });
    }

    sendMessage(type, data = {}) {
        if (!this.connected) {
            console.error('❌ 未连接到服务器');
            return;
        }

        const message = {
            type: type,
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            timestamp: Date.now(),
            encrypted: false,
            data: data
        };

        console.log('📤 发送消息:', JSON.stringify(message, null, 2));
        this.ws.send(JSON.stringify(message));
    }

    sendHeartbeatResponse() {
        this.sendMessage('heartbeat_response', {
            clientId: this.clientId,
            timestamp: Date.now()
        });
    }

    // 测试系统信息请求
    testSystemInfoRequest() {
        this.sendMessage('system_info_request', {
            requestId: `req_${Date.now()}`
        });
    }

    // 测试插件列表请求
    testPluginListRequest() {
        this.sendMessage('plugin_list_request', {
            requestId: `req_${Date.now()}`
        });
    }

    // 测试客户端列表请求
    testClientListRequest() {
        this.sendMessage('client_list_request', {
            requestId: `req_${Date.now()}`
        });
    }

    // 测试插件执行请求
    testPluginExecuteRequest() {
        this.sendMessage('plugin_execute_request', {
            pluginName: 'file_manager',
            command: 'list_files',
            params: {
                path: 'C:\\',
                recursive: false
            }
        });
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 测试函数
async function runProperTests() {
    const client = new ProperTestClient();
    
    try {
        // 连接测试
        console.log('🧪 开始测试WebSocket连接...');
        await client.connect();
        
        // 等待一下让服务器处理连接
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 测试系统信息请求
        console.log('\n🧪 测试系统信息请求...');
        client.testSystemInfoRequest();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 测试插件列表请求
        console.log('\n🧪 测试插件列表请求...');
        client.testPluginListRequest();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 测试客户端列表请求
        console.log('\n🧪 测试客户端列表请求...');
        client.testClientListRequest();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 测试插件执行请求
        console.log('\n🧪 测试插件执行请求...');
        client.testPluginExecuteRequest();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('\n✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        client.disconnect();
        process.exit(0);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runProperTests();
}

module.exports = ProperTestClient;
