const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

// 任务状态
const TASK_STATUS = {
    PENDING: 'pending',
    QUEUED: 'queued',
    ASSIGNED: 'assigned',
    EXECUTING: 'executing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

// 任务优先级
const TASK_PRIORITY = {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
};

// 任务类型
const TASK_TYPE = {
    PLUGIN_EXECUTE: 'plugin_execute',
    FILE_TRANSFER: 'file_transfer',
    SYSTEM_INFO: 'system_info',
    HEALTH_CHECK: 'health_check',
    CUSTOM: 'custom'
};

class TaskScheduler extends EventEmitter {
    constructor(options = {}) {
        super();
        this.maxConcurrentTasks = options.maxConcurrentTasks || 50;
        this.defaultTimeout = options.defaultTimeout || 300000; // 5分钟
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 5000; // 5秒
        
        // 任务存储
        this.tasks = new Map();
        this.taskQueue = [];
        this.executingTasks = new Map();
        
        // 客户端任务映射
        this.clientTasks = new Map();
        
        // 调度器状态
        this.isRunning = false;
        this.schedulerInterval = null;
        
        // 统计信息
        this.stats = {
            totalTasks: 0,
            queuedTasks: 0,
            executingTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            cancelledTasks: 0,
            timeoutTasks: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0
        };
    }

    // 启动任务调度器
    start() {
        if (this.isRunning) {
            console.warn('任务调度器已在运行');
            return;
        }
        
        this.isRunning = true;
        this.schedulerInterval = setInterval(() => {
            this.processTaskQueue();
        }, 1000); // 每秒检查一次
        
        console.log('任务调度器已启动');
        this.emit('schedulerStarted');
    }

    // 停止任务调度器
    stop() {
        if (!this.isRunning) {
            return;
        }
        
        this.isRunning = false;
        
        if (this.schedulerInterval) {
            clearInterval(this.schedulerInterval);
            this.schedulerInterval = null;
        }
        
        // 取消所有待执行任务
        for (const task of this.taskQueue) {
            this.cancelTask(task.id, '调度器停止');
        }
        
        console.log('任务调度器已停止');
        this.emit('schedulerStopped');
    }

    // 创建任务
    createTask(taskData) {
        const task = {
            id: uuidv4(),
            type: taskData.type || TASK_TYPE.CUSTOM,
            name: taskData.name || 'Unnamed Task',
            description: taskData.description || '',
            payload: taskData.payload || {},
            targetClients: taskData.targetClients || [],
            priority: taskData.priority || TASK_PRIORITY.NORMAL,
            timeout: taskData.timeout || this.defaultTimeout,
            retryCount: 0,
            maxRetries: taskData.maxRetries || this.retryAttempts,
            status: TASK_STATUS.PENDING,
            createdAt: Date.now(),
            scheduledAt: taskData.scheduledAt || Date.now(),
            startedAt: null,
            completedAt: null,
            result: null,
            error: null,
            metadata: taskData.metadata || {},
            dependencies: taskData.dependencies || [],
            callback: taskData.callback || null
        };

        this.tasks.set(task.id, task);
        this.stats.totalTasks++;

        console.log(`创建任务: ${task.name} (${task.id})`);
        this.emit('taskCreated', task);

        return task.id;
    }

    // 调度任务
    scheduleTask(taskId, delay = 0) {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error(`任务不存在: ${taskId}`);
        }

        if (task.status !== TASK_STATUS.PENDING) {
            throw new Error(`任务状态无效: ${task.status}`);
        }

        // 检查依赖
        if (!this.checkTaskDependencies(task)) {
            throw new Error('任务依赖未满足');
        }

        task.scheduledAt = Date.now() + delay;
        task.status = TASK_STATUS.QUEUED;
        
        // 插入队列并按优先级和调度时间排序
        this.insertTaskIntoQueue(task);
        this.stats.queuedTasks++;

        console.log(`调度任务: ${task.name} (${task.id}), 延迟: ${delay}ms`);
        this.emit('taskScheduled', task);

        return true;
    }

    // 将任务插入队列（按优先级排序）
    insertTaskIntoQueue(task) {
        const priorityOrder = {
            [TASK_PRIORITY.URGENT]: 4,
            [TASK_PRIORITY.HIGH]: 3,
            [TASK_PRIORITY.NORMAL]: 2,
            [TASK_PRIORITY.LOW]: 1
        };

        let insertIndex = this.taskQueue.length;
        
        for (let i = 0; i < this.taskQueue.length; i++) {
            const queuedTask = this.taskQueue[i];
            const taskPriority = priorityOrder[task.priority] || 2;
            const queuedPriority = priorityOrder[queuedTask.priority] || 2;
            
            if (taskPriority > queuedPriority || 
                (taskPriority === queuedPriority && task.scheduledAt < queuedTask.scheduledAt)) {
                insertIndex = i;
                break;
            }
        }
        
        this.taskQueue.splice(insertIndex, 0, task);
    }

    // 处理任务队列
    processTaskQueue() {
        if (!this.isRunning || this.taskQueue.length === 0) {
            return;
        }

        const now = Date.now();
        const maxConcurrent = this.maxConcurrentTasks - this.executingTasks.size;
        
        let processedCount = 0;
        
        for (let i = this.taskQueue.length - 1; i >= 0 && processedCount < maxConcurrent; i--) {
            const task = this.taskQueue[i];
            
            // 检查是否到了执行时间
            if (task.scheduledAt <= now) {
                // 从队列中移除
                this.taskQueue.splice(i, 1);
                this.stats.queuedTasks--;
                
                // 开始执行
                this.executeTask(task);
                processedCount++;
            }
        }
    }

    // 执行任务
    executeTask(task) {
        task.status = TASK_STATUS.EXECUTING;
        task.startedAt = Date.now();
        
        this.executingTasks.set(task.id, task);
        this.stats.executingTasks++;

        console.log(`开始执行任务: ${task.name} (${task.id})`);
        this.emit('taskStarted', task);

        // 设置超时
        const timeoutId = setTimeout(() => {
            this.timeoutTask(task.id);
        }, task.timeout);

        // 分配任务到客户端
        this.assignTaskToClients(task, timeoutId);
    }

    // 分配任务到客户端
    assignTaskToClients(task, timeoutId) {
        if (task.targetClients.length === 0) {
            this.failTask(task.id, '没有目标客户端');
            clearTimeout(timeoutId);
            return;
        }

        task.status = TASK_STATUS.ASSIGNED;
        task.timeoutId = timeoutId;
        task.assignedClients = new Set(task.targetClients);
        task.clientResults = new Map();

        // 为每个客户端记录任务分配
        for (const clientId of task.targetClients) {
            if (!this.clientTasks.has(clientId)) {
                this.clientTasks.set(clientId, new Set());
            }
            this.clientTasks.get(clientId).add(task.id);
        }

        console.log(`分配任务到客户端: ${task.name} -> [${task.targetClients.join(', ')}]`);
        this.emit('taskAssigned', task);
    }

    // 更新任务进度
    updateTaskProgress(taskId, clientId, progress, message = null) {
        const task = this.tasks.get(taskId);
        if (!task || task.status !== TASK_STATUS.ASSIGNED) {
            return false;
        }

        if (!task.clientProgress) {
            task.clientProgress = new Map();
        }

        task.clientProgress.set(clientId, {
            progress: progress,
            message: message,
            updatedAt: Date.now()
        });

        this.emit('taskProgress', task, clientId, progress, message);
        return true;
    }

    // 提交任务结果
    submitTaskResult(taskId, clientId, success, result = null, error = null) {
        const task = this.tasks.get(taskId);
        if (!task || task.status !== TASK_STATUS.ASSIGNED) {
            console.warn(`无效的任务结果提交: ${taskId} from ${clientId}`);
            return false;
        }

        if (!task.assignedClients.has(clientId)) {
            console.warn(`客户端 ${clientId} 未分配任务 ${taskId}`);
            return false;
        }

        // 记录客户端结果
        task.clientResults.set(clientId, {
            success: success,
            result: result,
            error: error,
            submittedAt: Date.now()
        });

        task.assignedClients.delete(clientId);

        // 清理客户端任务映射
        const clientTaskSet = this.clientTasks.get(clientId);
        if (clientTaskSet) {
            clientTaskSet.delete(taskId);
        }

        console.log(`收到任务结果: ${task.name} from ${clientId}, 成功: ${success}`);
        this.emit('taskResultSubmitted', task, clientId, success, result, error);

        // 检查是否所有客户端都已返回结果
        if (task.assignedClients.size === 0) {
            this.completeTask(taskId);
        }

        return true;
    }

    // 完成任务
    completeTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return false;
        }

        task.status = TASK_STATUS.COMPLETED;
        task.completedAt = Date.now();
        
        // 清理超时定时器
        if (task.timeoutId) {
            clearTimeout(task.timeoutId);
            delete task.timeoutId;
        }

        // 从执行中任务移除
        this.executingTasks.delete(taskId);
        this.stats.executingTasks--;
        this.stats.completedTasks++;

        // 计算执行时间
        const executionTime = task.completedAt - task.startedAt;
        this.stats.totalExecutionTime += executionTime;
        this.stats.averageExecutionTime = this.stats.totalExecutionTime / this.stats.completedTasks;

        // 汇总结果
        this.aggregateTaskResults(task);

        console.log(`任务完成: ${task.name} (${task.id}), 耗时: ${executionTime}ms`);
        this.emit('taskCompleted', task);

        // 执行回调
        if (task.callback && typeof task.callback === 'function') {
            try {
                task.callback(null, task.result);
            } catch (error) {
                console.error('任务回调执行失败:', error);
            }
        }

        return true;
    }

    // 汇总任务结果
    aggregateTaskResults(task) {
        const results = Array.from(task.clientResults.values());
        const successfulResults = results.filter(r => r.success);
        const failedResults = results.filter(r => !r.success);

        task.result = {
            totalClients: results.length,
            successfulClients: successfulResults.length,
            failedClients: failedResults.length,
            successRate: (successfulResults.length / results.length * 100).toFixed(2),
            results: results,
            aggregatedData: this.aggregateClientData(successfulResults.map(r => r.result))
        };
    }

    // 聚合客户端数据
    aggregateClientData(resultData) {
        if (resultData.length === 0) {
            return null;
        }

        // 简单的数据聚合逻辑，可根据需要扩展
        return {
            count: resultData.length,
            data: resultData
        };
    }

    // 任务失败
    failTask(taskId, reason) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return false;
        }

        task.status = TASK_STATUS.FAILED;
        task.completedAt = Date.now();
        task.error = reason;

        // 清理超时定时器
        if (task.timeoutId) {
            clearTimeout(task.timeoutId);
            delete task.timeoutId;
        }

        // 从执行中任务移除
        this.executingTasks.delete(taskId);
        this.stats.executingTasks--;

        // 检查是否需要重试
        if (task.retryCount < task.maxRetries) {
            task.retryCount++;
            console.log(`任务失败，准备重试: ${task.name} (${task.retryCount}/${task.maxRetries})`);
            
            // 重置任务状态
            task.status = TASK_STATUS.PENDING;
            task.assignedClients = null;
            task.clientResults = null;
            task.clientProgress = null;
            
            // 延迟重试
            setTimeout(() => {
                this.scheduleTask(taskId, this.retryDelay * task.retryCount);
            }, this.retryDelay);
            
            this.emit('taskRetry', task);
            return true;
        }

        this.stats.failedTasks++;

        // 清理客户端任务映射
        if (task.assignedClients) {
            for (const clientId of task.assignedClients) {
                const clientTaskSet = this.clientTasks.get(clientId);
                if (clientTaskSet) {
                    clientTaskSet.delete(taskId);
                }
            }
        }

        console.error(`任务失败: ${task.name} (${task.id}), 原因: ${reason}`);
        this.emit('taskFailed', task, reason);

        // 执行回调
        if (task.callback && typeof task.callback === 'function') {
            try {
                task.callback(new Error(reason), null);
            } catch (error) {
                console.error('任务回调执行失败:', error);
            }
        }

        return true;
    }

    // 任务超时
    timeoutTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task || task.status !== TASK_STATUS.ASSIGNED) {
            return false;
        }

        task.status = TASK_STATUS.TIMEOUT;
        task.completedAt = Date.now();
        task.error = '任务执行超时';

        // 从执行中任务移除
        this.executingTasks.delete(taskId);
        this.stats.executingTasks--;
        this.stats.timeoutTasks++;

        // 清理客户端任务映射
        if (task.assignedClients) {
            for (const clientId of task.assignedClients) {
                const clientTaskSet = this.clientTasks.get(clientId);
                if (clientTaskSet) {
                    clientTaskSet.delete(taskId);
                }
            }
        }

        console.warn(`任务超时: ${task.name} (${task.id})`);
        this.emit('taskTimeout', task);

        return true;
    }

    // 取消任务
    cancelTask(taskId, reason = '手动取消') {
        const task = this.tasks.get(taskId);
        if (!task) {
            return false;
        }

        const oldStatus = task.status;
        task.status = TASK_STATUS.CANCELLED;
        task.completedAt = Date.now();
        task.error = reason;

        // 清理超时定时器
        if (task.timeoutId) {
            clearTimeout(task.timeoutId);
            delete task.timeoutId;
        }

        // 从相应的队列或执行中移除
        if (oldStatus === TASK_STATUS.QUEUED) {
            const index = this.taskQueue.findIndex(t => t.id === taskId);
            if (index !== -1) {
                this.taskQueue.splice(index, 1);
                this.stats.queuedTasks--;
            }
        } else if (oldStatus === TASK_STATUS.EXECUTING || oldStatus === TASK_STATUS.ASSIGNED) {
            this.executingTasks.delete(taskId);
            this.stats.executingTasks--;
        }

        this.stats.cancelledTasks++;

        // 清理客户端任务映射
        if (task.assignedClients) {
            for (const clientId of task.assignedClients) {
                const clientTaskSet = this.clientTasks.get(clientId);
                if (clientTaskSet) {
                    clientTaskSet.delete(taskId);
                }
            }
        }

        console.log(`取消任务: ${task.name} (${task.id}), 原因: ${reason}`);
        this.emit('taskCancelled', task, reason);

        return true;
    }

    // 检查任务依赖
    checkTaskDependencies(task) {
        if (!task.dependencies || task.dependencies.length === 0) {
            return true;
        }

        for (const depTaskId of task.dependencies) {
            const depTask = this.tasks.get(depTaskId);
            if (!depTask || depTask.status !== TASK_STATUS.COMPLETED) {
                return false;
            }
        }

        return true;
    }

    // 获取任务信息
    getTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return null;
        }

        return {
            id: task.id,
            type: task.type,
            name: task.name,
            description: task.description,
            status: task.status,
            priority: task.priority,
            createdAt: task.createdAt,
            scheduledAt: task.scheduledAt,
            startedAt: task.startedAt,
            completedAt: task.completedAt,
            targetClients: task.targetClients,
            result: task.result,
            error: task.error,
            retryCount: task.retryCount,
            maxRetries: task.maxRetries,
            metadata: task.metadata
        };
    }

    // 获取所有任务
    getAllTasks() {
        return Array.from(this.tasks.keys()).map(taskId => this.getTask(taskId));
    }

    // 获取客户端任务
    getClientTasks(clientId) {
        const clientTaskSet = this.clientTasks.get(clientId);
        if (!clientTaskSet) {
            return [];
        }

        return Array.from(clientTaskSet).map(taskId => this.getTask(taskId));
    }

    // 移除客户端
    removeClient(clientId) {
        const clientTaskSet = this.clientTasks.get(clientId);
        if (clientTaskSet) {
            // 标记客户端相关任务失败
            for (const taskId of clientTaskSet) {
                const task = this.tasks.get(taskId);
                if (task && task.status === TASK_STATUS.ASSIGNED) {
                    // 从分配的客户端中移除
                    if (task.assignedClients) {
                        task.assignedClients.delete(clientId);
                        
                        // 如果没有其他客户端，则任务失败
                        if (task.assignedClients.size === 0) {
                            this.failTask(taskId, '所有分配的客户端已断开连接');
                        }
                    }
                }
            }
            
            this.clientTasks.delete(clientId);
        }

        console.log(`移除客户端任务映射: ${clientId}`);
    }

    // 获取统计信息
    getStats() {
        return {
            ...this.stats,
            queueLength: this.taskQueue.length,
            executingTasksCount: this.executingTasks.size,
            totalClients: this.clientTasks.size,
            successRate: this.stats.totalTasks > 0 ? 
                (this.stats.completedTasks / this.stats.totalTasks * 100).toFixed(2) : 0
        };
    }

    // 清理已完成的任务
    cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24小时
        const now = Date.now();
        let cleanedCount = 0;

        for (const [taskId, task] of this.tasks) {
            if (task.completedAt && (now - task.completedAt) > maxAge) {
                if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED, TASK_STATUS.TIMEOUT].includes(task.status)) {
                    this.tasks.delete(taskId);
                    cleanedCount++;
                }
            }
        }

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期任务`);
        }

        return cleanedCount;
    }
}

module.exports = {
    TaskScheduler,
    TASK_STATUS,
    TASK_PRIORITY,
    TASK_TYPE
};