# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\windows\client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\windows\client\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles C:\Users\<USER>\Desktop\windows\client\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\windows\client\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named DistributedPluginClient

# Build rule for target.
DistributedPluginClient: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 DistributedPluginClient
.PHONY : DistributedPluginClient

# fast build rule for target.
DistributedPluginClient/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/build
.PHONY : DistributedPluginClient/fast

src/common.obj: src/common.cpp.obj
.PHONY : src/common.obj

# target to build an object file
src/common.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/common.cpp.obj
.PHONY : src/common.cpp.obj

src/common.i: src/common.cpp.i
.PHONY : src/common.i

# target to preprocess a source file
src/common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/common.cpp.i
.PHONY : src/common.cpp.i

src/common.s: src/common.cpp.s
.PHONY : src/common.s

# target to generate assembly for a file
src/common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/common.cpp.s
.PHONY : src/common.cpp.s

src/crypto/xor/xor_crypto.obj: src/crypto/xor/xor_crypto.cpp.obj
.PHONY : src/crypto/xor/xor_crypto.obj

# target to build an object file
src/crypto/xor/xor_crypto.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.obj
.PHONY : src/crypto/xor/xor_crypto.cpp.obj

src/crypto/xor/xor_crypto.i: src/crypto/xor/xor_crypto.cpp.i
.PHONY : src/crypto/xor/xor_crypto.i

# target to preprocess a source file
src/crypto/xor/xor_crypto.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.i
.PHONY : src/crypto/xor/xor_crypto.cpp.i

src/crypto/xor/xor_crypto.s: src/crypto/xor/xor_crypto.cpp.s
.PHONY : src/crypto/xor/xor_crypto.s

# target to generate assembly for a file
src/crypto/xor/xor_crypto.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/crypto/xor/xor_crypto.cpp.s
.PHONY : src/crypto/xor/xor_crypto.cpp.s

src/loader/pe_loader/pe_loader.obj: src/loader/pe_loader/pe_loader.cpp.obj
.PHONY : src/loader/pe_loader/pe_loader.obj

# target to build an object file
src/loader/pe_loader/pe_loader.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.obj
.PHONY : src/loader/pe_loader/pe_loader.cpp.obj

src/loader/pe_loader/pe_loader.i: src/loader/pe_loader/pe_loader.cpp.i
.PHONY : src/loader/pe_loader/pe_loader.i

# target to preprocess a source file
src/loader/pe_loader/pe_loader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.i
.PHONY : src/loader/pe_loader/pe_loader.cpp.i

src/loader/pe_loader/pe_loader.s: src/loader/pe_loader/pe_loader.cpp.s
.PHONY : src/loader/pe_loader/pe_loader.s

# target to generate assembly for a file
src/loader/pe_loader/pe_loader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/loader/pe_loader/pe_loader.cpp.s
.PHONY : src/loader/pe_loader/pe_loader.cpp.s

src/main.obj: src/main.cpp.obj
.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/network/protocol/protocol_handler.obj: src/network/protocol/protocol_handler.cpp.obj
.PHONY : src/network/protocol/protocol_handler.obj

# target to build an object file
src/network/protocol/protocol_handler.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.obj
.PHONY : src/network/protocol/protocol_handler.cpp.obj

src/network/protocol/protocol_handler.i: src/network/protocol/protocol_handler.cpp.i
.PHONY : src/network/protocol/protocol_handler.i

# target to preprocess a source file
src/network/protocol/protocol_handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.i
.PHONY : src/network/protocol/protocol_handler.cpp.i

src/network/protocol/protocol_handler.s: src/network/protocol/protocol_handler.cpp.s
.PHONY : src/network/protocol/protocol_handler.s

# target to generate assembly for a file
src/network/protocol/protocol_handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/protocol/protocol_handler.cpp.s
.PHONY : src/network/protocol/protocol_handler.cpp.s

src/network/websocket/simple_websocket_client_no_ssl.obj: src/network/websocket/simple_websocket_client_no_ssl.cpp.obj
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.obj

# target to build an object file
src/network/websocket/simple_websocket_client_no_ssl.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.obj
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.cpp.obj

src/network/websocket/simple_websocket_client_no_ssl.i: src/network/websocket/simple_websocket_client_no_ssl.cpp.i
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.i

# target to preprocess a source file
src/network/websocket/simple_websocket_client_no_ssl.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.i
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.cpp.i

src/network/websocket/simple_websocket_client_no_ssl.s: src/network/websocket/simple_websocket_client_no_ssl.cpp.s
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.s

# target to generate assembly for a file
src/network/websocket/simple_websocket_client_no_ssl.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/simple_websocket_client_no_ssl.cpp.s
.PHONY : src/network/websocket/simple_websocket_client_no_ssl.cpp.s

src/network/websocket/websocket_client.obj: src/network/websocket/websocket_client.cpp.obj
.PHONY : src/network/websocket/websocket_client.obj

# target to build an object file
src/network/websocket/websocket_client.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.obj
.PHONY : src/network/websocket/websocket_client.cpp.obj

src/network/websocket/websocket_client.i: src/network/websocket/websocket_client.cpp.i
.PHONY : src/network/websocket/websocket_client.i

# target to preprocess a source file
src/network/websocket/websocket_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.i
.PHONY : src/network/websocket/websocket_client.cpp.i

src/network/websocket/websocket_client.s: src/network/websocket/websocket_client.cpp.s
.PHONY : src/network/websocket/websocket_client.s

# target to generate assembly for a file
src/network/websocket/websocket_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/network/websocket/websocket_client.cpp.s
.PHONY : src/network/websocket/websocket_client.cpp.s

src/obfuscator/string_obfuscator/string_obfuscator.obj: src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.obj

# target to build an object file
src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.cpp.obj

src/obfuscator/string_obfuscator/string_obfuscator.i: src/obfuscator/string_obfuscator/string_obfuscator.cpp.i
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.i

# target to preprocess a source file
src/obfuscator/string_obfuscator/string_obfuscator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.i
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.cpp.i

src/obfuscator/string_obfuscator/string_obfuscator.s: src/obfuscator/string_obfuscator/string_obfuscator.cpp.s
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.s

# target to generate assembly for a file
src/obfuscator/string_obfuscator/string_obfuscator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/obfuscator/string_obfuscator/string_obfuscator.cpp.s
.PHONY : src/obfuscator/string_obfuscator/string_obfuscator.cpp.s

src/plugins/executor/plugin_executor.obj: src/plugins/executor/plugin_executor.cpp.obj
.PHONY : src/plugins/executor/plugin_executor.obj

# target to build an object file
src/plugins/executor/plugin_executor.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.obj
.PHONY : src/plugins/executor/plugin_executor.cpp.obj

src/plugins/executor/plugin_executor.i: src/plugins/executor/plugin_executor.cpp.i
.PHONY : src/plugins/executor/plugin_executor.i

# target to preprocess a source file
src/plugins/executor/plugin_executor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.i
.PHONY : src/plugins/executor/plugin_executor.cpp.i

src/plugins/executor/plugin_executor.s: src/plugins/executor/plugin_executor.cpp.s
.PHONY : src/plugins/executor/plugin_executor.s

# target to generate assembly for a file
src/plugins/executor/plugin_executor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\DistributedPluginClient.dir\build.make CMakeFiles/DistributedPluginClient.dir/src/plugins/executor/plugin_executor.cpp.s
.PHONY : src/plugins/executor/plugin_executor.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... DistributedPluginClient
	@echo ... src/common.obj
	@echo ... src/common.i
	@echo ... src/common.s
	@echo ... src/crypto/xor/xor_crypto.obj
	@echo ... src/crypto/xor/xor_crypto.i
	@echo ... src/crypto/xor/xor_crypto.s
	@echo ... src/loader/pe_loader/pe_loader.obj
	@echo ... src/loader/pe_loader/pe_loader.i
	@echo ... src/loader/pe_loader/pe_loader.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/network/protocol/protocol_handler.obj
	@echo ... src/network/protocol/protocol_handler.i
	@echo ... src/network/protocol/protocol_handler.s
	@echo ... src/network/websocket/simple_websocket_client_no_ssl.obj
	@echo ... src/network/websocket/simple_websocket_client_no_ssl.i
	@echo ... src/network/websocket/simple_websocket_client_no_ssl.s
	@echo ... src/network/websocket/websocket_client.obj
	@echo ... src/network/websocket/websocket_client.i
	@echo ... src/network/websocket/websocket_client.s
	@echo ... src/obfuscator/string_obfuscator/string_obfuscator.obj
	@echo ... src/obfuscator/string_obfuscator/string_obfuscator.i
	@echo ... src/obfuscator/string_obfuscator/string_obfuscator.s
	@echo ... src/plugins/executor/plugin_executor.obj
	@echo ... src/plugins/executor/plugin_executor.i
	@echo ... src/plugins/executor/plugin_executor.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

