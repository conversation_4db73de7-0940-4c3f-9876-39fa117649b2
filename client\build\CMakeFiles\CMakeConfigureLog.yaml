
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/Downloads/w64devkit/bin/cc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/3.31.4/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/Downloads/w64devkit/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/3.31.4/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z"
      binary: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/Downloads/w64devkit/bin/mingw32-make.exe -f Makefile cmTC_a1c8e/fast
        make  -f CMakeFiles\\cmTC_a1c8e.dir\\build.make CMakeFiles/cmTC_a1c8e.dir/build
        make[1]: Entering directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z'
        Building C object CMakeFiles/cmTC_a1c8e.dir/CMakeCCompilerABI.c.obj
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=cc
        Target: x86_64-w64-mingw32
        Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c,c++,fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\'
         C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -isysroot C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_a1c8e.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccEY4EzP.s
        GNU C23 (GCC) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
        End of search list.
        Compiler executable checksum: f2f7cbe133d2cb4b0d97b7f2d3426b72
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\'
         as -v -o CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccEY4EzP.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/
        LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_a1c8e.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_a1c8e.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_a1c8e.dir/objects.a
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\ar.exe qc CMakeFiles\\cmTC_a1c8e.dir/objects.a @CMakeFiles\\cmTC_a1c8e.dir\\objects1.rsp
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a -Wl,--no-whole-archive -o cmTC_a1c8e.exe -Wl,--out-implib,libcmTC_a1c8e.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=cc
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c,c++,fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (GCC) 
        COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/
        LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a1c8e.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_a1c8e.'
         C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_a1c8e.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a --no-whole-archive --out-implib libcmTC_a1c8e.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin/ld.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_a1c8e.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a --no-whole-archive --out-implib libcmTC_a1c8e.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a1c8e.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_a1c8e.'
        make[1]: Leaving directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        end of search list found
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [C:/Users/<USER>/Downloads/w64devkit/include]
        implicit include dirs: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/Downloads/w64devkit/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/Downloads/w64devkit/bin/mingw32-make.exe -f Makefile cmTC_a1c8e/fast]
        ignore line: [make  -f CMakeFiles\\cmTC_a1c8e.dir\\build.make CMakeFiles/cmTC_a1c8e.dir/build]
        ignore line: [make[1]: Entering directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-b8zr3z']
        ignore line: [Building C object CMakeFiles/cmTC_a1c8e.dir/CMakeCCompilerABI.c.obj]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=cc]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c c++ fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\']
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -isysroot C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_a1c8e.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccEY4EzP.s]
        ignore line: [GNU C23 (GCC) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f2f7cbe133d2cb4b0d97b7f2d3426b72]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\']
        ignore line: [ as -v -o CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccEY4EzP.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_a1c8e.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_a1c8e.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_a1c8e.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_a1c8e.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\ar.exe qc CMakeFiles\\cmTC_a1c8e.dir/objects.a @CMakeFiles\\cmTC_a1c8e.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a -Wl --no-whole-archive -o cmTC_a1c8e.exe -Wl --out-implib libcmTC_a1c8e.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=cc]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c c++ fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a1c8e.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_a1c8e.']
        link line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_a1c8e.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a --no-whole-archive --out-implib libcmTC_a1c8e.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [--sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_a1c8e.exe] ==> ignore
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_a1c8e.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_a1c8e.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        ignore line: [collect2 version 15.1.0]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin/ld.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_a1c8e.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a1c8e.dir/objects.a --no-whole-archive --out-implib libcmTC_a1c8e.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'C': C:/Users/<USER>/Downloads/w64devkit/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/crt2.o]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/Downloads/w64devkit/lib]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/Downloads/w64devkit/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/Downloads/w64devkit/lib/crt2.o;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/Downloads/w64devkit/lib/gcc;C:/Users/<USER>/Downloads/w64devkit/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Users/<USER>/Downloads/w64devkit/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i"
      binary: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/Downloads/w64devkit/bin/mingw32-make.exe -f Makefile cmTC_b869a/fast
        make  -f CMakeFiles\\cmTC_b869a.dir\\build.make CMakeFiles/cmTC_b869a.dir/build
        make[1]: Entering directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i'
        Building CXX object CMakeFiles/cmTC_b869a.dir/CMakeCXXCompilerABI.cpp.obj
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=c++
        Target: x86_64-w64-mingw32
        Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c,c++,fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\'
         C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -isysroot C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_b869a.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczZmKtw.s
        GNU C++17 (GCC) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
        End of search list.
        Compiler executable checksum: f456542120caaa142db7cd97798dd521
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\'
         as -v -o CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczZmKtw.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/
        LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_b869a.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_b869a.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_b869a.dir/objects.a
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\ar.exe qc CMakeFiles\\cmTC_b869a.dir/objects.a @CMakeFiles\\cmTC_b869a.dir\\objects1.rsp
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\c++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a -Wl,--no-whole-archive -o cmTC_b869a.exe -Wl,--out-implib,libcmTC_b869a.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=c++
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c,c++,fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (GCC) 
        COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/
        LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b869a.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_b869a.'
         C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_b869a.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a --no-whole-archive --out-implib libcmTC_b869a.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin/ld.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_b869a.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a --no-whole-archive --out-implib libcmTC_b869a.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b869a.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_b869a.'
        make[1]: Leaving directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        end of search list found
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [C:/Users/<USER>/Downloads/w64devkit/include]
        implicit include dirs: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/Downloads/w64devkit/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/Downloads/w64devkit/bin/mingw32-make.exe -f Makefile cmTC_b869a/fast]
        ignore line: [make  -f CMakeFiles\\cmTC_b869a.dir\\build.make CMakeFiles/cmTC_b869a.dir/build]
        ignore line: [make[1]: Entering directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-qlmi6i']
        ignore line: [Building CXX object CMakeFiles/cmTC_b869a.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=c++]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c c++ fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\']
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -isysroot C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_b869a.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczZmKtw.s]
        ignore line: [GNU C++17 (GCC) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/Downloads/w64devkit/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: f456542120caaa142db7cd97798dd521]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\']
        ignore line: [ as -v -o CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cczZmKtw.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles\\cmTC_b869a.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_b869a.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_b869a.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_b869a.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\ar.exe qc CMakeFiles\\cmTC_b869a.dir/objects.a @CMakeFiles\\cmTC_b869a.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\c++.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a -Wl --no-whole-archive -o cmTC_b869a.exe -Wl --out-implib libcmTC_b869a.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=c++]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: /gcc-15.1.0/configure --prefix=/w64devkit --with-sysroot=/w64devkit --with-native-system-header-dir=/include --target=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-static --disable-shared --with-pic --with-gmp-include=/deps/include --with-gmp-lib=/deps/lib --with-mpc-include=/deps/include --with-mpc-lib=/deps/lib --with-mpfr-include=/deps/include --with-mpfr-lib=/deps/lib --enable-languages=c c++ fortran --enable-libgomp --enable-threads=posix --enable-version-specific-runtime-libs --disable-libstdcxx-verbose --disable-dependency-tracking --disable-lto --disable-multilib --disable-nls --disable-win32-registry --enable-mingw-wildcard CFLAGS_FOR_TARGET=-Os CXXFLAGS_FOR_TARGET=-Os LDFLAGS_FOR_TARGET=-s CFLAGS=-Os CXXFLAGS=-Os LDFLAGS=-s]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b869a.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_b869a.']
        link line: [ C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_b869a.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a --no-whole-archive --out-implib libcmTC_b869a.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [--sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_b869a.exe] ==> ignore
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_b869a.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_b869a.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        ignore line: [collect2 version 15.1.0]
        ignore line: [C:\\Users\\<USER>\\Downloads\\w64devkit\\bin/ld.exe --sysroot=C:/Users/<USER>/Downloads/w64devkit/bin/../../w64devkit -m i386pep -Bdynamic -o cmTC_b869a.exe C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_b869a.dir/objects.a --no-whole-archive --out-implib libcmTC_b869a.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'CXX': C:/Users/<USER>/Downloads/w64devkit/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/crt2.o]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc] ==> [C:/Users/<USER>/Downloads/w64devkit/lib/gcc]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/Downloads/w64devkit/lib]
        collapse library dir [C:/Users/<USER>/Downloads/w64devkit/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/Downloads/w64devkit/lib]
        implicit libs: [stdc++;mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/Downloads/w64devkit/lib/crt2.o;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/Downloads/w64devkit/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/Downloads/w64devkit/lib/gcc;C:/Users/<USER>/Downloads/w64devkit/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Users/<USER>/Downloads/w64devkit/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.44
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:21 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-wmewsg"
      binary: "C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-wmewsg"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-wmewsg'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/Downloads/w64devkit/bin/mingw32-make.exe -f Makefile cmTC_76872/fast
        make  -f CMakeFiles\\cmTC_76872.dir\\build.make CMakeFiles/cmTC_76872.dir/build
        make[1]: Entering directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-wmewsg'
        Building C object CMakeFiles/cmTC_76872.dir/src.c.obj
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles\\cmTC_76872.dir\\src.c.obj -c C:\\Users\\<USER>\\Desktop\\windows\\client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wmewsg\\src.c
        Linking C executable cmTC_76872.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_76872.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_76872.dir/objects.a
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\ar.exe qc CMakeFiles\\cmTC_76872.dir/objects.a @CMakeFiles\\cmTC_76872.dir\\objects1.rsp
        C:\\Users\\<USER>\\Downloads\\w64devkit\\bin\\cc.exe -Wl,--whole-archive CMakeFiles\\cmTC_76872.dir/objects.a -Wl,--no-whole-archive -o cmTC_76872.exe -Wl,--out-implib,libcmTC_76872.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_76872.dir\\linkLibs.rsp
        make[1]: Leaving directory 'C:/Users/<USER>/Desktop/windows/client/build/CMakeFiles/CMakeScratch/TryCompile-wmewsg'
        
      exitCode: 0
...
