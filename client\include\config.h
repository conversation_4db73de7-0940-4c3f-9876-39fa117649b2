#pragma once

#include <string>
#include <cstdint>

namespace Config {
    constexpr const char* DEFAULT_SERVER_HOST = "127.0.0.1";
    constexpr uint16_t DEFAULT_SERVER_PORT = 8080;
    constexpr uint32_t RECONNECT_INTERVAL_MS = 5000;
    constexpr uint32_t HEARTBEAT_INTERVAL_MS = 30000;
    constexpr uint32_t MAX_RECONNECT_ATTEMPTS = 10;
    constexpr uint32_t MAX_PAYLOAD_SIZE = 10 * 1024 * 1024; // 10MB
    constexpr uint32_t CHUNK_SIZE = 64 * 1024; // 64KB
    constexpr uint32_t THREAD_POOL_SIZE = 4;
    constexpr uint32_t MAX_CONCURRENT_PLUGINS = 16;
    
    // Security
    constexpr uint8_t XOR_KEY[] = {0x42, 0x69, 0x6E, 0x61, 0x72, 0x79, 0x4B, 0x65, 0x79};
    constexpr size_t XOR_KEY_SIZE = sizeof(XOR_KEY);
    
    // Protocol
    const std::string PROTOCOL_VERSION = "1.0";
    const std::string CLIENT_TYPE = "cpp_client";
    
    // Paths
    const std::string PLUGINS_DIR = "plugins";
    const std::string LOGS_DIR = "logs";
    const std::string TEMP_DIR = "temp";
}