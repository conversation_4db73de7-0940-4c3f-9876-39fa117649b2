#include "common.h"

// Logger implementation
void Logger::Log(Level level, const std::string& message, const char* file, int line) {
    if (level < current_level_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    std::string timestamp = GetTimestamp();
    std::string level_str = LevelToString(level);
    std::string file_info = "";
    
    if (file && line > 0) {
        std::string filename = file;
        size_t last_slash = filename.find_last_of("/\\");
        if (last_slash != std::string::npos) {
            filename = filename.substr(last_slash + 1);
        }
        file_info = " [" + filename + ":" + std::to_string(line) + "]";
    }
    
    std::string log_message = timestamp + " " + level_str + file_info + " " + message;
    
    // Output to console
    std::cout << log_message << std::endl;
    
    // Output to file if available
    if (log_file_.is_open()) {
        log_file_ << log_message << std::endl;
        log_file_.flush();
    }
}

void Logger::SetOutputFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    if (log_file_.is_open()) {
        log_file_.close();
    }
    
    log_file_.open(filename, std::ios::app);
    if (!log_file_.is_open()) {
        std::cerr << "Failed to open log file: " << filename << std::endl;
    }
}

std::string Logger::LevelToString(Level level) {
    switch (level) {
        case Level::DEBUG:   return "[DEBUG]";
        case Level::INFO:    return "[INFO] ";
        case Level::WARNING: return "[WARN] ";
        case Level::ERROR:   return "[ERROR]";
        default:             return "[UNKNOWN]";
    }
}

std::string Logger::GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}