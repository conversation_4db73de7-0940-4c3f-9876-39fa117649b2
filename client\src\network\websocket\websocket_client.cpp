#include "websocket_client.h"

namespace Network {

    WebSocketClient::WebSocketClient()
        : client_(std::make_unique<SimpleWebSocketClient>()) {
        
        LOG_INFO("WebSocket client initialized");
    }

    WebSocketClient::~WebSocketClient() {
        Stop();
    }

    bool WebSocketClient::Connect(const std::string& host, uint16_t port, const std::string& path) {
        LOG_INFO("Connecting to WebSocket server: " + host + ":" + std::to_string(port) + path);
        return client_->Connect(host, port, path);
    }

    void WebSocketClient::Disconnect() {
        LOG_INFO("Disconnecting from WebSocket server");
        client_->Disconnect();
    }

    bool WebSocketClient::Send(const std::string& message) {
        return client_->Send(message);
    }

    bool WebSocketClient::SendBinary(const ByteArray& data) {
        return client_->SendBinary(data);
    }

    void WebSocketClient::SetMessageCallback(MessageCallback callback) {
        client_->SetMessageCallback(callback);
    }

    void WebSocketClient::SetStateCallback(StateCallback callback) {
        client_->SetStateCallback(callback);
    }

    WebSocketClient::ConnectionState WebSocketClient::GetState() const {
        return client_->GetState();
    }

    bool WebSocketClient::IsConnected() const {
        return client_->IsConnected();
    }

    void WebSocketClient::StartReconnectLoop() {
        LOG_INFO("Starting reconnect loop");
        client_->StartReconnectLoop();
    }

    void WebSocketClient::StopReconnectLoop() {
        LOG_INFO("Stopping reconnect loop");
        client_->StopReconnectLoop();
    }

    void WebSocketClient::Run() {
        LOG_INFO("WebSocket client started");
        client_->Run();
    }

    void WebSocketClient::Stop() {
        LOG_INFO("WebSocket client stopping");
        client_->Stop();
    }

    void WebSocketClient::SetServerParams(const std::string& host, uint16_t port, const std::string& path) {
        // Store connection parameters in the SimpleWebSocketClient for reconnect
        // This is a bit of a hack, but we need to set the server parameters before starting reconnect
        client_->server_host_ = host;
        client_->server_port_ = port;
        client_->server_path_ = path;
        client_->server_uri_ = client_->BuildUri(host, port, path);
    }

} // namespace Network